# Utils 工具类层

## 📋 层级用途

Utils层是ParticleSystemToGfxFramework转换工具的工具类层，提供通用的辅助功能和工具方法。

## 🎯 设计职责

- **通用功能封装**：封装可复用的工具方法
- **辅助功能支持**：为其他层提供辅助功能
- **代码复用**：避免重复代码，提高开发效率
- **独立性**：工具类应该是无状态和独立的

## 📁 文件说明

### ParticleSystemValidator.cs
- **职责**：粒子系统验证相关的工具方法
- **功能**：
  - 验证ParticleSystem是否符合转换条件
  - 检查Max Particles是否为1
  - 验证是否使用了禁用的模块
  - 检查Render Mode和Mesh设置

### GfxFrameworkHelper.cs
- **职责**：GfxFramework相关的辅助方法
- **功能**：
  - 检查GameObject是否包含GfxRoot_Unity组件
  - 提供GfxFramework组件的常用操作
  - 封装GfxFramework的配置和设置方法
  - 提供GfxFramework相关的查询方法

### ConversionLogger.cs
- **职责**：转换过程的日志记录工具
- **功能**：
  - 统一的日志记录接口
  - 不同级别的日志输出（Info、Warning、Error）
  - 日志格式化和美化
  - 转换统计信息记录

## 🛠️ 工具类设计模式

### 静态工具类
```csharp
public static class ParticleSystemValidator
{
    public static bool IsValidForConversion(ParticleSystem ps)
    {
        // 验证逻辑
    }
}
```

### 单例模式（如果需要状态）
```csharp
public class ConversionLogger
{
    private static ConversionLogger _instance;
    public static ConversionLogger Instance => _instance ??= new ConversionLogger();
    
    private ConversionLogger() { }
}
```

### 扩展方法
```csharp
public static class ParticleSystemExtensions
{
    public static bool HasGfxRoot(this GameObject gameObject)
    {
        return gameObject.GetComponent<GfxRoot_Unity>() != null;
    }
}
```

## 🔧 功能分类

### 验证工具
- ParticleSystem属性验证
- GameObject组件验证
- Prefab有效性验证
- 文件路径验证

### 转换辅助
- 属性值转换和映射
- 组件查找和操作
- 文件路径处理
- 数据格式转换

### 日志工具
- 结构化日志记录
- 错误信息格式化
- 调试信息输出
- 性能统计

## 📝 编码规范

### 静态方法优先
```csharp
// 优先使用静态方法
public static class ValidationHelper
{
    public static bool IsValid(object obj) => obj != null;
}
```

### 参数验证
```csharp
public static bool ValidateParticleSystem(ParticleSystem ps)
{
    if (ps == null)
        throw new ArgumentNullException(nameof(ps));
    
    // 验证逻辑
}
```

### 异常处理
```csharp
public static T SafeGetComponent<T>(GameObject go) where T : Component
{
    try
    {
        return go.GetComponent<T>();
    }
    catch (Exception ex)
    {
        ConversionLogger.LogError($"获取组件失败：{ex.Message}");
        return null;
    }
}
```

## 🔗 依赖关系

- **依赖**：Unity引擎API、.NET基础类库
- **被依赖**：Core层、UI层、Data层

## 🧪 测试策略

### 单元测试
- 每个工具方法都应有对应的单元测试
- 测试边界条件和异常情况
- 使用Mock对象隔离外部依赖

### 测试示例
```csharp
[Test]
public void ValidateParticleSystem_WithValidPS_ReturnsTrue()
{
    // Arrange
    var ps = CreateValidParticleSystem();
    
    // Act
    var result = ParticleSystemValidator.IsValidForConversion(ps);
    
    // Assert
    Assert.IsTrue(result);
}
```

## 📋 最佳实践

### 1. 无状态设计
- 工具类应该是无状态的
- 避免静态字段存储状态
- 每次调用都应该是独立的

### 2. 异常安全
- 合理处理异常情况
- 提供有意义的错误信息
- 不要吞噬异常

### 3. 性能考虑
- 避免重复计算
- 缓存昂贵的操作结果
- 使用合适的数据结构

### 4. 文档完善
- 为每个公开方法添加XML文档注释
- 说明参数要求和返回值
- 提供使用示例

## 🎯 扩展建议

- 可添加配置管理工具类
- 可添加文件操作工具类
- 可添加Unity资源管理工具类
- 可添加性能分析工具类
