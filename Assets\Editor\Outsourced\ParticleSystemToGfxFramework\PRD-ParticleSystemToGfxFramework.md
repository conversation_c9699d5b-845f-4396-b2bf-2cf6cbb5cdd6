# 粒子系统转Gfx框架工具 PRD文档

## 1. 产品概述

### 1.1 背景

在Unity特效开发中，对于某些简单的视觉效果，使用ParticleSystem组件可能会造成性能浪费。特别是当粒子系统的最大粒子数(Max Particles)设置为1且不使用复杂模块时，这些效果可以通过更高效的GfxFramework实现，从而提升游戏性能。

### 1.2 目标

开发一个Unity编辑器扩展工具，能够自动将符合条件的ParticleSystem组件转换为基于GfxFramework的实现，保持视觉效果一致的同时提高运行效率。

### 1.3 用户群体

- **Unity特效美术师**：需要优化特效性能的美术人员
- **技术美术**：负责特效技术实现和优化的专业人员  
- **游戏优化工程师**：专注于游戏性能优化的技术人员

### 1.4 使用场景说明

#### 目标用户：
- **Unity特效美术师**：需要优化特效性能的美术人员
- **技术美术**：负责特效技术实现和优化的专业人员
- **游戏优化工程师**：专注于游戏性能优化的技术人员

#### 典型使用场景：

1. **性能优化场景**
   - 游戏中存在大量简单的单粒子效果（如UI特效、简单装饰效果）
   - 需要减少ParticleSystem组件的性能开销
   - 项目后期性能优化阶段

2. **特效制作流程**
   - 美术师使用ParticleSystem快速制作原型效果
   - 通过工具一键转换为高效的GfxFramework实现
   - 保持视觉效果不变的前提下提升性能

3. **批量优化场景**
   - 项目中存在大量符合条件的简单粒子效果
   - 需要统一转换为GfxFramework实现
   - 减少项目整体的渲染负担

#### 使用条件限制：
- 仅适用于Max Particles=1的简单粒子系统
- 不支持使用复杂模块的粒子系统
- 要求Render Mode为Mesh且Mesh不为空
- 需要在包含GfxRoot_Unity组件的特效Prefab中使用

## 2. 功能需求

### 2.1 流程图

```mermaid
flowchart TD
    A[右键点击场景Hierarchy视图中的特效Prefab] --> B{检查是否有GfxRoot_Unity组件}
    B -->|没有| C[警告：不是特效Prefab，停止转换。]
    B -->|有| D[搜索Prefab内所有ParticleSystem组件]
 
    D --> E[筛选Max Particles=1 && 不使用Shape、Velocity over Lifetime、Limit Velocity over Lifetime、Inherit Velocity、Force over Lifetime、Color by Speed、Size by Speed、Rotation by Speed、External Forces、Noise、Collision、Triggers、Sub Emitters、Texture Sheet Animation、Light、Trails模块 && Renderer的Render Mode为Mesh && Mesh不为空 的ParticleSystem]
    E --> F{是否有符合条件的ParticleSystem}
    F -->|没有| G[显示提示:没有可转换的粒子系统]
    F -->|有| H[创建转换列表]
    H --> J[遍历每个待转换的ParticleSystem]
    J --> K[获取ParticleSystem的Renderer组件]
    K --> L[检查GameObject是否已有MeshFilter和MeshRenderer]
    L -->|没有| M[添加MeshFilter组件并设置Mesh为ParticleSystem的Renderer使用的Mesh]
    L -->|有| N[更新现有MeshFilter的Mesh为ParticleSystem的Renderer的Mesh]
    M --> O[添加MeshRenderer组件并设置材质球为ParticleSystem的Renderer的Material]
    N --> O
    O --> O1[添加GfxAnimation_Unity组件并勾选"显示控制"，将"开始时间"设置为ParticleSystem的Start Delay，将"结束时间"设置为后者的Start Delay+Start Lifetime。]
    O1 --> P{检查是否有Color over Lifetime}
    P -->|有| Q[添加GfxMaterialAnimation_Unity组件，添加"颜色动画"和_Color属性，并将_Color设置为Color over lifetime的Color。]
    P -->|没有| R{检查是否有Rotation over Lifetime}
    Q --> R
    R -->|有| S[更新GfxAnimation_Unity组件，勾选"旋转动画"，将X坐标、Y坐标、Z坐标设置为Rotation over Lifetime的Angular Velocity或Curve。]
    R -->|没有| T{检查是否有Size over Lifetime}
    S --> T
    T -->|有| U[更新GfxAnimation_Unity组件，勾选"缩放动画"，将X缩放、Y缩放、Z缩放设置为Size over Lifetime的X、Y、Z Curve。]
    T -->|没有| Y
    U --> Y[禁用原ParticleSystem组件]
    Y --> Z{是否还有待处理的ParticleSystem}
    Z -->|有| J
    Z -->|没有| AA[生成新Prefab并保存到原目录下加后缀_Gfx]
    AA --> AB[显示转换完成提示] 
```

### 2.2 核心功能

#### 粒子系统识别与筛选
- 识别Prefab中所有ParticleSystem组件
- 筛选出符合转换条件的粒子系统

#### 转换为Gfx框架
- 将ParticleSystem的Mesh转换为MeshFilter和MeshRenderer
- 将ParticleSystem的动画属性转换为GfxAnimation_Unity
- 将ParticleSystem的材质动画转换为GfxMaterialAnimation_Unity

#### Prefab生成
- 生成新的Prefab保留原始效果
- 保存到原目录下并添加_Gfx后缀

### 2.3 筛选条件

符合以下全部条件的ParticleSystem将被转换：

- Max Particles = 1
- 不使用以下模块：
  - Shape
  - Velocity over Lifetime
  - Limit Velocity over Lifetime
  - Inherit Velocity
  - Force over Lifetime
  - Color by Speed
  - Size by Speed
  - Rotation by Speed
  - External Forces
  - Noise
  - Collision
  - Triggers
  - Sub Emitters
  - Texture Sheet Animation
  - Light
  - Trails
- Renderer的Render Mode为Mesh
- Mesh不为空

### 2.4 转换映射关系

| ParticleSystem属性 | Gfx框架对应组件/属性 |
|-------------------|-------------------|
| Mesh | MeshFilter.mesh |
| Material | MeshRenderer.material |
| Start Delay | GfxAnimation_Unity.开始时间 |
| Start Lifetime | GfxAnimation_Unity.结束时间 - 开始时间 |
| Color over Lifetime | GfxMaterialAnimation_Unity._Color属性 |
| Rotation over Lifetime | GfxAnimation_Unity.旋转动画 |
| Size over Lifetime | GfxAnimation_Unity.缩放动画 |

## 3. 用户使用方法

### 3.1 工具入口

该工具无UI，在Unity编辑器的Hierarchy视图中右键点击特效Prefab，在弹出菜单中选择"粒子转Gfx"选项，开始转换。

### 3.2 转换结果提示

- **转换成功**：在UnityConsole中显示成功转换的粒子系统数量和新Prefab路径
- **无可转换项**：显示"没有符合条件的粒子系统"提示
- **非特效Prefab**：显示"不是特效Prefab，停止转换"警告

## 4. 程序架构设计

### 4.1 文件夹结构设计

```
Assets/Editor/Outsourced/ParticleSystemToGfxFramework/
├── 📄 PRD-ParticleSystemToGfxFramework.md          # 产品需求文档
├── 📄 GfxFramework架构总结.md                      # 架构文档
├── 📄 任务清单.md                                  # 开发任务清单
├── 📁 Scripts/                                    # 核心代码目录
│   ├── 📁 Core/                                   # 核心转换逻辑
│   │   ├── 📄 ParticleSystemAnalyzer.cs           # 粒子系统分析器
│   │   ├── 📄 GfxComponentFactory.cs              # Gfx组件工厂
│   │   ├── 📄 PropertyMapper.cs                   # 属性映射器
│   │   └── 📄 PrefabGenerator.cs                  # Prefab生成器
│   ├── 📁 Data/                                   # 数据模型
│   │   ├── 📄 ConversionData.cs                   # 转换数据模型
│   │   ├── 📄 ParticleSystemInfo.cs               # 粒子系统信息
│   │   └── 📄 ConversionResult.cs                 # 转换结果
│   ├── 📁 UI/                                     # 编辑器界面
│   │   └── 📄 ParticleToGfxContextMenu.cs         # 右键菜单
│   └── 📁 Utils/                                  # 工具类
│       ├── 📄 ParticleSystemValidator.cs          # 粒子系统验证器
│       ├── 📄 GfxFrameworkHelper.cs               # Gfx框架辅助类
│       └── 📄 ConversionLogger.cs                 # 转换日志
└── 📁 Tests/                                      # 单元测试
    ├── 📄 ParticleSystemAnalyzerTests.cs
    ├── 📄 PropertyMapperTests.cs
    └── 📄 TestData/                               # 测试数据
```

### 4.2 程序架构设计（MVC模式）

```mermaid
graph TB
    subgraph "UI Layer (View)"
        A[ParticleToGfxContextMenu] --> B[右键菜单入口]
    end

    subgraph "Controller Layer"
        C[ConversionController] --> D[转换流程控制]
        C --> E[错误处理]
        C --> F[结果反馈]
    end

    subgraph "Service Layer"
        G[ParticleSystemAnalyzer] --> H[粒子系统分析]
        I[PropertyMapper] --> J[属性映射转换]
        K[GfxComponentFactory] --> L[Gfx组件创建]
        M[PrefabGenerator] --> N[Prefab生成]
    end

    subgraph "Data Layer (Model)"
        O[ConversionData] --> P[转换配置数据]
        Q[ParticleSystemInfo] --> R[粒子系统信息]
        S[ConversionResult] --> T[转换结果数据]
    end

    subgraph "Utils Layer"
        U[ParticleSystemValidator] --> V[验证逻辑]
        W[GfxFrameworkHelper] --> X[Gfx框架操作]
        Y[ConversionLogger] --> Z[日志记录]
    end

    A --> C
    C --> G
    C --> I
    C --> K
    C --> M
    G --> O
    I --> Q
    K --> S
    M --> S
    C --> U
    C --> W
    C --> Y
```

### 4.3 核心组件设计

#### 4.3.1 ConversionController (控制器)
```csharp
// 职责：统一控制转换流程，协调各个组件
public class ConversionController
{
    - ParticleSystemAnalyzer analyzer
    - PropertyMapper mapper
    - GfxComponentFactory factory
    - PrefabGenerator generator

    + ConvertParticleSystemToGfx(GameObject prefab) : ConversionResult
    + ValidateConversionRequirements(GameObject prefab) : bool
}
```

#### 4.3.2 ParticleSystemAnalyzer (分析器)
```csharp
// 职责：分析和筛选符合条件的ParticleSystem
public class ParticleSystemAnalyzer
{
    + AnalyzePrefab(GameObject prefab) : List<ParticleSystemInfo>
    + FilterValidParticleSystems(ParticleSystem[] systems) : List<ParticleSystem>
    + ExtractParticleSystemProperties(ParticleSystem ps) : ParticleSystemInfo
}
```

#### 4.3.3 PropertyMapper (属性映射器)
```csharp
// 职责：将ParticleSystem属性映射到GfxFramework属性
public class PropertyMapper
{
    + MapToGfxAnimation(ParticleSystemInfo info) : GfxAnimationData
    + MapToGfxMaterialAnimation(ParticleSystemInfo info) : GfxMaterialAnimationData
    + MapTimeProperties(ParticleSystemInfo info) : GfxTimeData
}
```

#### 4.3.4 GfxComponentFactory (组件工厂)
```csharp
// 职责：创建和配置GfxFramework组件
public class GfxComponentFactory
{
    + CreateGfxAnimation(GameObject target, GfxAnimationData data) : GfxAnimation_Unity
    + CreateGfxMaterialAnimation(GameObject target, GfxMaterialAnimationData data) : GfxMaterialAnimation_Unity
    + CreateMeshComponents(GameObject target, Mesh mesh, Material material) : void
}
```

#### 4.3.5 PrefabGenerator (Prefab生成器)
```csharp
// 职责：生成新的Prefab文件
public class PrefabGenerator
{
    + GenerateGfxPrefab(GameObject original, ConversionData data) : GameObject
    + SavePrefabToPath(GameObject prefab, string originalPath) : string
}
```

### 4.4 设计原则应用

#### 4.4.1 SOLID原则
- **单一职责**：每个类只负责一个明确的功能
- **开闭原则**：通过接口和抽象类支持扩展
- **接口隔离**：定义细粒度的接口
- **依赖倒置**：依赖抽象而非具体实现

#### 4.4.2 MVC分离
- **Model**：数据模型（ConversionData, ParticleSystemInfo等）
- **View**：UI界面（ParticleToGfxContextMenu）
- **Controller**：业务逻辑控制（ConversionController）

#### 4.4.3 模块化设计
- **Core**：核心转换逻辑，可独立测试
- **UI**：界面层，与业务逻辑解耦
- **Utils**：工具类，提供通用功能
- **Data**：数据模型，清晰的数据结构

### 4.5 与GfxFramework的对接

#### 4.5.1 依赖关系
```csharp
// 引用GfxFramework的核心组件
using TKFrame.extension.GfxFramework.Unity;
using TKFrame.extension.GfxFramework.Core;
```

#### 4.5.2 组件创建方式
```csharp
// 通过AddComponent方式创建Gfx组件
var gfxAnim = targetGameObject.AddComponent<GfxAnimation_Unity>();
var gfxMatAnim = targetGameObject.AddComponent<GfxMaterialAnimation_Unity>();
```

#### 4.5.3 属性设置方式
```csharp
// 直接设置GfxFramework组件的公开属性
gfxAnim.m_timeSystem.m_startTime = startDelay;
gfxAnim.m_activeModule.m_enable = true;
```

### 4.6 优势分析

1. **可维护性**：清晰的模块划分，便于后续维护和扩展
2. **可测试性**：每个组件职责单一，便于编写单元测试
3. **可扩展性**：通过接口设计，支持未来功能扩展
4. **代码复用**：工具类和数据模型可在其他项目中复用
5. **错误处理**：集中的错误处理和日志记录机制
