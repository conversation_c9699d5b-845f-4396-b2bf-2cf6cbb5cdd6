using UnityEngine;
using System;

namespace ParticleSystemToGfxFramework.Data
{
    /// <summary>
    /// 粒子系统验证配置，支持配置化的验证规则
    /// </summary>
    [CreateAssetMenu(fileName = "ConversionRules", menuName = "ParticleToGfx/Validation Config")]
    public class ValidationConfig : ScriptableObject
    {
        [Header("配置信息")]
        [SerializeField] private string version = "1.0";
        [SerializeField] private string description = "粒子系统转换验证规则配置";
        
        [Header("基础属性验证规则")]
        [SerializeField] private BasicPropertyRule[] basicRules = new BasicPropertyRule[0];
        
        [Header("模块状态验证规则")]
        [SerializeField] private ModuleStateRule[] moduleRules = new ModuleStateRule[0];
        
        [Header("自定义表达式验证规则")]
        [SerializeField] private ExpressionRule[] expressionRules = new ExpressionRule[0];
        
        /// <summary>
        /// 配置版本
        /// </summary>
        public string Version => version;
        
        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description => description;
        
        /// <summary>
        /// 基础属性规则
        /// </summary>
        public BasicPropertyRule[] BasicRules => basicRules;
        
        /// <summary>
        /// 模块状态规则
        /// </summary>
        public ModuleStateRule[] ModuleRules => moduleRules;
        
        /// <summary>
        /// 表达式规则
        /// </summary>
        public ExpressionRule[] ExpressionRules => expressionRules;
        
        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            // 检查是否至少有一个验证规则
            return basicRules.Length > 0 || moduleRules.Length > 0 || expressionRules.Length > 0;
        }
        
        /// <summary>
        /// 获取所有规则的总数
        /// </summary>
        public int GetTotalRuleCount()
        {
            return basicRules.Length + moduleRules.Length + expressionRules.Length;
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static ValidationConfig CreateDefault()
        {
            var config = CreateInstance<ValidationConfig>();
            config.version = "1.0";
            config.description = "默认粒子系统转换验证规则";
            
            // 添加PRD中的基础规则
            config.basicRules = new BasicPropertyRule[]
            {
                new BasicPropertyRule
                {
                    name = "maxParticles",
                    propertyPath = "main.maxParticles",
                    comparisonOperator = ComparisonOperator.Equals,
                    expectedValue = "1",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Max Particles必须等于1"
                }
            };
            
            // 添加模块状态规则
            config.moduleRules = new ModuleStateRule[]
            {
                new ModuleStateRule
                {
                    name = "forbiddenModules",
                    moduleNames = new string[]
                    {
                        "shape", "velocityOverLifetime", "limitVelocityOverLifetime", "inheritVelocity",
                        "forceOverLifetime", "colorBySpeed", "sizeBySpeed", "rotationBySpeed",
                        "externalForces", "noise", "collision", "trigger", "subEmitters",
                        "textureSheetAnimation", "lights", "trails"
                    },
                    allowEnabled = false,
                    severity = ValidationSeverity.Error,
                    errorMessage = "禁止启用的模块: {moduleName}"
                }
            };
            
            // 添加表达式规则
            config.expressionRules = new ExpressionRule[]
            {
                new ExpressionRule
                {
                    name = "renderModeAndMesh",
                    expression = "renderer.renderMode == 4 && renderer.mesh != null",
                    severity = ValidationSeverity.Error,
                    errorMessage = "必须使用Mesh渲染模式且Mesh不为空"
                }
            };
            
            return config;
        }
    }
}
