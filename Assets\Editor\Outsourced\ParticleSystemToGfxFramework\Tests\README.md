# Tests 单元测试层

## 📋 层级用途

Tests层是ParticleSystemToGfxFramework转换工具的测试层，包含所有单元测试、集成测试和测试数据。

## 🎯 设计职责

- **质量保证**：确保代码功能正确性和稳定性
- **回归测试**：防止新功能破坏现有功能
- **文档作用**：测试用例作为代码使用示例
- **重构支持**：为代码重构提供安全保障

## 📁 文件说明

### ParticleSystemAnalyzerTests.cs
- **测试目标**：ParticleSystemAnalyzer类
- **测试内容**：
  - 粒子系统分析功能
  - 筛选条件验证
  - 属性提取准确性
  - 边界条件处理

### PropertyMapperTests.cs
- **测试目标**：PropertyMapper类
- **测试内容**：
  - 属性映射准确性
  - 数据类型转换
  - 默认值处理
  - 异常情况处理

### TestData/
- **用途**：存放测试用的数据文件
- **内容**：
  - 测试用的Prefab文件
  - 测试配置文件
  - 预期结果数据
  - Mock对象定义

## 🧪 测试框架

### Unity Test Framework
```csharp
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;

[TestFixture]
public class ParticleSystemAnalyzerTests
{
    [Test]
    public void AnalyzePrefab_WithValidPrefab_ReturnsCorrectCount()
    {
        // Arrange, Act, Assert
    }
}
```

### 测试分类
- **[Test]**：普通单元测试
- **[UnityTest]**：需要Unity协程的测试
- **[TestCase]**：参数化测试
- **[Category]**：测试分类标记

## 🏗️ 测试结构

### AAA模式
```csharp
[Test]
public void TestMethod_Condition_ExpectedResult()
{
    // Arrange - 准备测试数据
    var input = CreateTestInput();
    
    // Act - 执行被测试方法
    var result = MethodUnderTest(input);
    
    // Assert - 验证结果
    Assert.AreEqual(expectedValue, result);
}
```

### 测试命名规范
- **方法名**：`MethodName_Condition_ExpectedResult`
- **类名**：`ClassNameTests`
- **描述性**：清楚表达测试意图

## 🎯 测试策略

### 单元测试
- **覆盖率目标**：核心业务逻辑达到80%以上
- **隔离性**：使用Mock对象隔离外部依赖
- **快速执行**：单个测试应在毫秒级完成

### 集成测试
- **端到端测试**：测试完整的转换流程
- **组件交互**：测试不同层之间的交互
- **真实环境**：使用真实的Unity组件

### 测试数据管理
```csharp
public class TestDataFactory
{
    public static GameObject CreateValidParticleSystemPrefab()
    {
        // 创建测试用的有效Prefab
    }
    
    public static ParticleSystem CreateInvalidParticleSystem()
    {
        // 创建测试用的无效粒子系统
    }
}
```

## 🔧 Mock和Stub

### 使用Moq框架
```csharp
[Test]
public void ConvertParticleSystem_WithMockFactory_CallsCorrectMethods()
{
    // Arrange
    var mockFactory = new Mock<IGfxComponentFactory>();
    var analyzer = new ParticleSystemAnalyzer(mockFactory.Object);
    
    // Act
    analyzer.ConvertParticleSystem(testParticleSystem);
    
    // Assert
    mockFactory.Verify(f => f.CreateGfxAnimation(It.IsAny<GameObject>()), Times.Once);
}
```

### 测试替身类型
- **Mock**：验证交互行为
- **Stub**：提供预定义响应
- **Fake**：简化的实现
- **Spy**：记录调用信息

## 📊 测试覆盖率

### 覆盖率目标
- **Core层**：90%以上
- **Utils层**：85%以上
- **Data层**：80%以上
- **UI层**：60%以上（手动测试为主）

### 覆盖率工具
- Unity Code Coverage Package
- 生成HTML覆盖率报告
- 持续集成中监控覆盖率

## 🚀 测试执行

### 本地执行
```bash
# 运行所有测试
Unity -batchmode -runTests -testPlatform EditMode

# 运行特定测试
Unity -batchmode -runTests -testFilter "ParticleSystemAnalyzerTests"
```

### 持续集成
- 每次提交自动运行测试
- 测试失败阻止合并
- 生成测试报告

## 📝 测试最佳实践

### 1. 测试独立性
- 每个测试应该独立运行
- 不依赖其他测试的执行顺序
- 清理测试产生的副作用

### 2. 测试可读性
```csharp
[Test]
public void ConvertParticleSystem_WhenMaxParticlesIsOne_ShouldCreateGfxAnimation()
{
    // 测试名称清楚表达意图
    // 测试代码简洁易懂
}
```

### 3. 边界条件测试
- 测试null值处理
- 测试空集合处理
- 测试极值情况
- 测试异常路径

### 4. 性能测试
```csharp
[Test, Performance]
public void ConvertLargeParticleSystem_ShouldCompleteWithinTimeLimit()
{
    using (Measure.Method())
    {
        // 执行性能测试
    }
}
```

## 📋 测试检查清单

- [ ] 所有公开方法都有对应测试
- [ ] 测试覆盖正常和异常路径
- [ ] 测试命名清晰描述意图
- [ ] 使用Mock隔离外部依赖
- [ ] 测试数据独立且可重复
- [ ] 测试执行快速且稳定
- [ ] 添加必要的性能测试
- [ ] 文档说明复杂测试逻辑
