using UnityEngine;
using TKFrame.extension.GfxFramework.Unity;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// GfxFramework相关的辅助工具类
    /// </summary>
    public static class GfxFrameworkHelper
    {
        /// <summary>
        /// 检查GameObject是否包含GfxRoot_Unity组件
        /// </summary>
        /// <param name="gameObject">要检查的GameObject</param>
        /// <returns>如果包含GfxRoot_Unity组件返回true，否则返回false</returns>
        public static bool HasGfxRoot(GameObject gameObject)
        {
            if (gameObject == null)
            {
                ConversionLogger.LogError("检查GfxRoot时GameObject为null");
                return false;
            }
            
            try
            {
                var gfxRoot = gameObject.GetComponent<GfxRoot_Unity>();
                bool hasGfxRoot = gfxRoot != null;
                
                ConversionLogger.LogInfo($"GfxRoot检测结果: {gameObject.name} {(hasGfxRoot ? "包含" : "不包含")} GfxRoot_Unity组件");
                
                return hasGfxRoot;
            }
            catch (System.Exception ex)
            {
                ConversionLogger.LogError($"检查GfxRoot时发生异常: {gameObject.name}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 获取GameObject上的GfxRoot_Unity组件
        /// </summary>
        /// <param name="gameObject">要获取组件的GameObject</param>
        /// <returns>GfxRoot_Unity组件，如果不存在则返回null</returns>
        public static GfxRoot_Unity GetGfxRoot(GameObject gameObject)
        {
            if (gameObject == null)
            {
                ConversionLogger.LogError("获取GfxRoot时GameObject为null");
                return null;
            }
            
            try
            {
                return gameObject.GetComponent<GfxRoot_Unity>();
            }
            catch (System.Exception ex)
            {
                ConversionLogger.LogError($"获取GfxRoot时发生异常: {gameObject.name}", ex);
                return null;
            }
        }
        
        /// <summary>
        /// 验证GfxFramework相关依赖是否可用
        /// </summary>
        /// <returns>如果依赖可用返回true，否则返回false</returns>
        public static bool ValidateGfxFrameworkDependencies()
        {
            try
            {
                // 尝试创建一个临时GameObject来测试GfxFramework组件是否可用
                var testObject = new GameObject("GfxFrameworkTest");
                var gfxRoot = testObject.AddComponent<GfxRoot_Unity>();
                
                bool isValid = gfxRoot != null;
                
                // 清理测试对象
                Object.DestroyImmediate(testObject);
                
                if (isValid)
                {
                    ConversionLogger.LogInfo("GfxFramework依赖验证成功");
                }
                else
                {
                    ConversionLogger.LogError("GfxFramework依赖验证失败：无法创建GfxRoot_Unity组件");
                }
                
                return isValid;
            }
            catch (System.Exception ex)
            {
                ConversionLogger.LogError("GfxFramework依赖验证失败", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 检查GameObject是否为有效的特效Prefab
        /// </summary>
        /// <param name="gameObject">要检查的GameObject</param>
        /// <returns>如果是有效的特效Prefab返回true，否则返回false</returns>
        public static bool IsValidEffectPrefab(GameObject gameObject)
        {
            if (gameObject == null)
            {
                ConversionLogger.LogWarning("检查特效Prefab时GameObject为null");
                return false;
            }
            
            // 检查是否包含GfxRoot_Unity组件
            if (!HasGfxRoot(gameObject))
            {
                ConversionLogger.LogWarning($"不是特效Prefab，停止转换: {gameObject.name} 缺少GfxRoot_Unity组件");
                return false;
            }
            
            ConversionLogger.LogInfo($"特效Prefab验证成功: {gameObject.name}");
            return true;
        }
    }
}
