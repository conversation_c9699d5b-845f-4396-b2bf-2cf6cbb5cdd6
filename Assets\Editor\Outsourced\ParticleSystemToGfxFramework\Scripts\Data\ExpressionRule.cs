using UnityEngine;
using System;

namespace ParticleSystemToGfxFramework.Data
{
    /// <summary>
    /// 验证严重级别
    /// </summary>
    public enum ValidationSeverity
    {
        Info = 0,
        Warning = 1,
        Error = 2
    }
    
    /// <summary>
    /// 比较操作符
    /// </summary>
    public enum ComparisonOperator
    {
        Equals,         // ==
        NotEquals,      // !=
        GreaterThan,    // >
        LessThan,       // <
        GreaterOrEqual, // >=
        LessOrEqual,    // <=
        NotNull,        // != null
        IsNull          // == null
    }
    
    /// <summary>
    /// 基础属性验证规则
    /// </summary>
    [Serializable]
    public class BasicPropertyRule
    {
        [Header("规则信息")]
        public string name = "";
        public string description = "";
        
        [Header("属性验证")]
        public string propertyPath = "";
        public ComparisonOperator comparisonOperator = ComparisonOperator.Equals;
        public string expectedValue = "";
        
        [Header("结果处理")]
        public ValidationSeverity severity = ValidationSeverity.Error;
        public string errorMessage = "";
        
        /// <summary>
        /// 规则是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(name) && 
                   !string.IsNullOrEmpty(propertyPath) && 
                   !string.IsNullOrEmpty(errorMessage);
        }
    }
    
    /// <summary>
    /// 模块状态验证规则
    /// </summary>
    [Serializable]
    public class ModuleStateRule
    {
        [Header("规则信息")]
        public string name = "";
        public string description = "";
        
        [Header("模块验证")]
        public string[] moduleNames = new string[0];
        public bool allowEnabled = false;
        
        [Header("结果处理")]
        public ValidationSeverity severity = ValidationSeverity.Error;
        public string errorMessage = "";
        
        /// <summary>
        /// 规则是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(name) && 
                   moduleNames != null && 
                   moduleNames.Length > 0 && 
                   !string.IsNullOrEmpty(errorMessage);
        }
    }
    
    /// <summary>
    /// 表达式验证规则
    /// </summary>
    [Serializable]
    public class ExpressionRule
    {
        [Header("规则信息")]
        public string name = "";
        public string description = "";
        
        [Header("表达式验证")]
        [TextArea(2, 4)]
        public string expression = "";
        
        [Header("结果处理")]
        public ValidationSeverity severity = ValidationSeverity.Error;
        public string errorMessage = "";
        
        /// <summary>
        /// 规则是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(name) && 
                   !string.IsNullOrEmpty(expression) && 
                   !string.IsNullOrEmpty(errorMessage);
        }
    }
    
    /// <summary>
    /// 验证结果
    /// </summary>
    [Serializable]
    public class ValidationResult
    {
        public bool isValid;
        public ValidationSeverity severity;
        public string ruleName;
        public string message;
        public string details;
        
        public ValidationResult(bool valid, ValidationSeverity sev, string rule, string msg, string det = "")
        {
            isValid = valid;
            severity = sev;
            ruleName = rule;
            message = msg;
            details = det;
        }
        
        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static ValidationResult Success(string ruleName)
        {
            return new ValidationResult(true, ValidationSeverity.Info, ruleName, "验证通过");
        }
        
        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static ValidationResult Failure(ValidationSeverity severity, string ruleName, string message, string details = "")
        {
            return new ValidationResult(false, severity, ruleName, message, details);
        }
        
        /// <summary>
        /// 是否为错误级别
        /// </summary>
        public bool IsError => severity == ValidationSeverity.Error;
        
        /// <summary>
        /// 是否为警告级别
        /// </summary>
        public bool IsWarning => severity == ValidationSeverity.Warning;
    }
}
