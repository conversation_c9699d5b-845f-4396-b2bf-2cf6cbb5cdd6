# TestData 测试数据目录

## 📋 目录用途

TestData目录存放ParticleSystemToGfxFramework转换工具测试所需的各种数据文件和资源。

## 🎯 设计职责

- **测试资源管理**：统一管理测试用的资源文件
- **数据隔离**：将测试数据与生产代码分离
- **可重复性**：确保测试数据的一致性和可重复性
- **版本控制**：测试数据纳入版本控制，保证团队一致性

## 📁 文件分类

### Prefab文件
- **ValidParticleSystemPrefab.prefab**：符合转换条件的测试Prefab
- **InvalidParticleSystemPrefab.prefab**：不符合转换条件的测试Prefab
- **ComplexParticleSystemPrefab.prefab**：复杂粒子系统测试Prefab
- **EmptyPrefab.prefab**：空的测试Prefab

### 配置文件
- **TestConversionConfig.json**：测试用的转换配置
- **ExpectedResults.json**：预期的转换结果数据
- **TestCases.json**：参数化测试用例数据

### 资源文件
- **TestMesh.asset**：测试用的Mesh资源
- **TestMaterial.mat**：测试用的Material资源
- **TestTexture.png**：测试用的纹理资源

## 🏗️ 数据结构设计

### 测试用例数据格式
```json
{
  "testCases": [
    {
      "name": "ValidParticleSystem",
      "description": "测试有效的粒子系统转换",
      "input": {
        "prefabPath": "TestData/ValidParticleSystemPrefab.prefab",
        "maxParticles": 1,
        "hasGfxRoot": true
      },
      "expected": {
        "shouldConvert": true,
        "componentCount": 2,
        "hasGfxAnimation": true,
        "hasMeshRenderer": true
      }
    }
  ]
}
```

### 预期结果数据格式
```json
{
  "conversionResults": {
    "ValidParticleSystem": {
      "success": true,
      "convertedCount": 1,
      "generatedPrefabPath": "TestData/ValidParticleSystemPrefab_Gfx.prefab",
      "components": [
        "GfxAnimation_Unity",
        "MeshFilter",
        "MeshRenderer"
      ]
    }
  }
}
```

## 🛠️ 测试数据工厂

### TestDataFactory类
```csharp
public static class TestDataFactory
{
    private const string TestDataPath = "Assets/Editor/Outsourced/ParticleSystemToGfxFramework/Tests/TestData/";
    
    public static GameObject LoadValidParticleSystemPrefab()
    {
        return AssetDatabase.LoadAssetAtPath<GameObject>(TestDataPath + "ValidParticleSystemPrefab.prefab");
    }
    
    public static TestCaseData LoadTestCases()
    {
        var json = File.ReadAllText(TestDataPath + "TestCases.json");
        return JsonUtility.FromJson<TestCaseData>(json);
    }
}
```

### 动态数据生成
```csharp
public static class DynamicTestDataGenerator
{
    public static GameObject CreateParticleSystemWithMaxParticles(int maxParticles)
    {
        var go = new GameObject("TestParticleSystem");
        var ps = go.AddComponent<ParticleSystem>();
        var main = ps.main;
        main.maxParticles = maxParticles;
        return go;
    }
}
```

## 📊 数据管理策略

### 版本控制
- 所有测试数据文件都纳入版本控制
- 使用.gitattributes确保二进制文件正确处理
- 定期清理过时的测试数据

### 数据更新
- 当产品功能变更时，同步更新测试数据
- 保持测试数据与PRD需求一致
- 记录数据变更历史

### 性能考虑
- 测试数据文件大小控制在合理范围内
- 使用延迟加载避免内存浪费
- 缓存频繁使用的测试数据

## 🧪 测试数据使用示例

### 加载测试Prefab
```csharp
[Test]
public void ConvertValidParticleSystem_ShouldSucceed()
{
    // Arrange
    var testPrefab = TestDataFactory.LoadValidParticleSystemPrefab();
    var converter = new ParticleSystemConverter();
    
    // Act
    var result = converter.Convert(testPrefab);
    
    // Assert
    Assert.IsTrue(result.Success);
}
```

### 参数化测试
```csharp
[TestCaseSource(nameof(GetTestCases))]
public void ConvertParticleSystem_WithVariousInputs_ProducesExpectedResults(TestCaseData testCase)
{
    // 使用测试用例数据进行参数化测试
}

private static IEnumerable<TestCaseData> GetTestCases()
{
    return TestDataFactory.LoadTestCases().TestCases;
}
```

## 📝 最佳实践

### 1. 数据命名规范
- 使用描述性的文件名
- 包含测试场景信息
- 遵循统一的命名约定

### 2. 数据最小化
- 只包含测试必需的数据
- 避免冗余和重复
- 保持数据文件简洁

### 3. 数据验证
```csharp
[SetUp]
public void ValidateTestData()
{
    Assert.IsNotNull(TestDataFactory.LoadValidParticleSystemPrefab(), 
        "测试数据文件缺失：ValidParticleSystemPrefab.prefab");
}
```

### 4. 清理策略
```csharp
[TearDown]
public void CleanupTestData()
{
    // 清理测试过程中生成的临时文件
    // 恢复测试环境到初始状态
}
```

## 📋 维护检查清单

- [ ] 测试数据文件完整且可访问
- [ ] 数据格式与代码期望一致
- [ ] 测试数据覆盖各种边界情况
- [ ] 定期验证数据文件有效性
- [ ] 文档说明每个数据文件的用途
- [ ] 清理过时和无用的测试数据
- [ ] 确保数据文件大小合理
- [ ] 版本控制配置正确
