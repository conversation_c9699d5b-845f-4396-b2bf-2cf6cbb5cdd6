# UI 编辑器界面层

## 📋 层级用途

UI层是ParticleSystemToGfxFramework转换工具的用户界面层，负责提供Unity编辑器中的用户交互入口。

## 🎯 设计职责

- **用户交互入口**：提供右键菜单等用户操作入口
- **界面逻辑控制**：处理用户输入和界面状态
- **结果展示**：向用户展示转换结果和反馈信息
- **用户体验**：确保操作简单直观

## 📁 文件说明

### ParticleToGfxContextMenu.cs
- **职责**：实现Unity编辑器右键菜单功能
- **功能**：
  - 在Hierarchy视图中添加"粒子转Gfx"右键菜单项
  - 验证选中对象是否为有效的特效Prefab
  - 调用转换控制器执行转换操作
  - 显示转换结果和错误信息

## 🏗️ Unity编辑器扩展模式

### MenuItem特性
```csharp
[MenuItem("GameObject/粒子转Gfx", false, 10)]
public static void ConvertParticleToGfx()
{
    // 菜单项实现
}
```

### 菜单项验证
```csharp
[MenuItem("GameObject/粒子转Gfx", true)]
public static bool ValidateConvertParticleToGfx()
{
    // 验证菜单项是否可用
    return Selection.activeGameObject != null;
}
```

### 右键上下文菜单
```csharp
[MenuItem("CONTEXT/Transform/粒子转Gfx")]
public static void ConvertParticleToGfxContext(MenuCommand command)
{
    // 右键上下文菜单实现
}
```

## 🎨 用户体验设计

### 操作流程
1. **选择对象**：用户在Hierarchy中选择特效Prefab
2. **右键菜单**：右键显示"粒子转Gfx"选项
3. **执行转换**：点击菜单项开始转换
4. **结果反馈**：在Console中显示转换结果

### 错误处理
- **无效选择**：菜单项置灰，提示选择有效对象
- **转换失败**：显示详细错误信息
- **无可转换项**：友好提示没有符合条件的粒子系统

### 用户反馈
```csharp
// 成功提示
Debug.Log($"转换成功！共转换 {count} 个粒子系统，新Prefab路径：{path}");

// 警告提示
Debug.LogWarning("没有找到符合条件的粒子系统");

// 错误提示
Debug.LogError("转换失败：不是有效的特效Prefab");
```

## 🔗 依赖关系

- **依赖**：Core层的ConversionController、Data层的数据模型
- **被依赖**：无（作为最上层的UI层）

## 📝 编码规范

### 静态方法
- UI层的菜单方法必须是静态方法
- 使用静态方法调用业务逻辑

### 异常处理
```csharp
try
{
    // 执行转换操作
    var result = controller.ConvertParticleSystemToGfx(selectedObject);
    ShowResult(result);
}
catch (Exception ex)
{
    Debug.LogError($"转换过程中发生错误：{ex.Message}");
}
```

### 用户输入验证
```csharp
// 验证用户选择
if (Selection.activeGameObject == null)
{
    Debug.LogWarning("请先选择一个GameObject");
    return;
}
```

## 🧪 测试策略

### 手动测试
- 测试菜单项显示和隐藏逻辑
- 测试不同选择状态下的菜单行为
- 测试转换成功和失败的用户反馈

### 集成测试
- 测试UI层与Core层的集成
- 测试完整的用户操作流程

## 📋 注意事项

1. **Unity编辑器API**：正确使用Unity编辑器扩展API
2. **线程安全**：UI操作必须在主线程中执行
3. **性能考虑**：避免在菜单验证中执行耗时操作
4. **用户体验**：提供清晰的操作反馈和错误信息
5. **本地化**：考虑多语言支持（如果需要）

## 🎯 未来扩展

- 可考虑添加进度条显示转换进度
- 可添加批量转换功能
- 可添加转换预览功能
- 可添加转换设置对话框
