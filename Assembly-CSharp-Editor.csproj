﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_rider_editor_plugin</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_rider_editor_plugin</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{811305DD-ACBC-CFFA-611B-77A0D0584AED}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2018_4_25;UNITY_2018_4;UNITY_2018;UNITY_INCLUDE_TESTS;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_SPRITES;ENABLE_GRID;ENABLE_TILEMAP;ENABLE_TERRAIN;ENABLE_TEXTURE_STREAMING;ENABLE_DIRECTOR;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_WEBCAM;ENABLE_WWW;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_HUB;ENABLE_CLOUD_PROJECT_ID;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_TIMELINE;ENABLE_EDITOR_METRICS;ENABLE_EDITOR_METRICS_CACHING;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;INCLUDE_DYNAMIC_GI;INCLUDE_GI;ENABLE_MONO_BDWGC;PLATFORM_SUPPORTS_MONO;INCLUDE_PUBNUB;ENABLE_VIDEO;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_LOCALIZATION;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_SUBSTANCE;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITYWEBREQUEST;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_EVENT_QUEUE;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;PLATFORM_SUPPORTS_ADS_ID;UNITY_CAN_SHOW_SPLASH_SCREEN;ENABLE_VR;ENABLE_AR;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_RUNTIME_PERMISSIONS;UNITY_ASTC_ONLY_DECOMPRESS;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;TKF_ALL_EXTEND;TKFE_NCEFFECT;ACGGAME_CLIENT;ENABLE_ASSET_BUNDLE_EXTEND;OUTSOURCE;OPTIMIZE_COLLECTION;TKF_EDITOR;ODIN_INSPECTOR;ODIN_INSPECTOR_3;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2018.4.25f1\Editor\Data\Managed/UnityEngine/UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2018.4.25f1\Editor\Data\Managed/UnityEditor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\ChessBattleData\LogicData\SpringBone\Editor\BoneWindow\BoneData.cs" />
    <Compile Include="Assets\ChessBattleData\LogicData\SpringBone\Editor\BoneWindow\BoneTools.cs" />
    <Compile Include="Assets\ChessBattleData\LogicData\SpringBone\Editor\BoneWindow\SpringBoneData.cs" />
    <Compile Include="Assets\Editor\ArtEditor\EditorObjExporter.cs" />
    <Compile Include="Assets\Editor\ArtEditor\ReferenceObjectCopyReNameReTarget.cs" />
    <Compile Include="Assets\Editor\ArtEditor\RefObjCopyReNameWindow.cs" />
    <Compile Include="Assets\Editor\ArtMergeTool\ArtMergeTool.cs" />
    <Compile Include="Assets\Editor\ArtResScanTool\ArtReportItem.cs" />
    <Compile Include="Assets\Editor\ArtResScanTool\ArtResInfo.cs" />
    <Compile Include="Assets\Editor\ArtResScanTool\ArtResScanScene.cs" />
    <Compile Include="Assets\Editor\ArtResScanTool\ArtResTotalReportItem.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ArtAssetbundlePack.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ArtResInfo.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ArtResMd5Assetbundle.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ArtResPathConfig.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ArtResPathConfigEditor.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ArtResPathUtil.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\SetBuildPathSetting.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\SetHeorCheckConfig.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResConfig\ZGameFolderInspector.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResOutputTool\BattleMapOutputTool.cs" />
    <Compile Include="Assets\Editor\ArtResTools\ArtResOutputTool\LittleLegendSceneOutputTool.cs" />
    <Compile Include="Assets\Editor\ArtResTools\GfxProfilerCompareWindow.cs" />
    <Compile Include="Assets\Editor\BuiltInsideResourceExplorer\CustomSeeBuiltInResourceEditor.cs" />
    <Compile Include="Assets\Editor\GUIDSearch.cs" />
    <Compile Include="Assets\Editor\Outsourced\AnimationAutoCreate\TeamLeaderAutoCreateUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\AnimationAutoCreate\TeamLeaderCfgGenTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\AnimationAutoCreate\TeamLeaderGenReportEditorPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\AnimationBroadcast\AnimationBroadcastToolWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\AnimSheetConfigEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\AnimSpringManagerConfigEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtCheckConfirmTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportAsset.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportAssetDefine.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportAssetWhiteList.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportAssetWhiteListInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportCore.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportPrefabAsset.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportSettingPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtExportTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtGlobalDefine.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\ArtResMergeResult.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\Lod\ArtAssetLodHandle.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\Lod\ArtAssetLodTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\Lod\ArtAssetModelLod.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\Tree\ArtTreeItem.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtExport\Tree\ArtTreeView.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameAnimationCurveEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameAssetPreview.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameChangeFogColorOnTriggerEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameEnvironmentControllerEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameEnvironmentPreview.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameExportCubeMap.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameIlluminShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameModelImporterEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameParticleEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameParticlePanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameSkinnedMeshRendererEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ACGameStandardShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\BakeLightCookie.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\BatchModifyAssetNameTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\ChainNodeInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\CharacterSettings.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\JKEffectsGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\ArtUtility\Editor\MaterialRefCleaner.cs" />
    <Compile Include="Assets\Editor\Outsourced\BattleMapPreviewEditor\BattleMapPreviewEditorControllerInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\BattleMapPreviewEditor\BattleMapPreviewSelectPopup.cs" />
    <Compile Include="Assets\Editor\Outsourced\CharacterEditor\HangPointEditor\CharacterHangPointBatchEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CharacterEditor\HangPointEditor\CharacterHangPointBoneRecordAllCfg.cs" />
    <Compile Include="Assets\Editor\Outsourced\CharacterEditor\HangPointEditor\CharacterHangPointCopyToPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\CharacterEditor\HangPointEditor\CharacterHangPointEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CharacterEditor\HangPointEditor\CharacterHangPointEditorController.cs" />
    <Compile Include="Assets\Editor\Outsourced\CharacterEditor\HangPointEditor\CharacterHangPointTypeEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerAniCfgEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerAniCfgEditorWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerAniCfgWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerAniSoundCfgEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerAniSoundCfgWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerMaterialCurveCfgEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ChessPlayer\ChessPlayerMaterialCurveCfgWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\Common\Editor\EditorWindowBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\Common\Editor\EditorWindowMgr.cs" />
    <Compile Include="Assets\Editor\Outsourced\ConstVar.cs" />
    <Compile Include="Assets\Editor\Outsourced\CoroutineEditor\EditorCoroutine.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\AssetUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\Color.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\CubeBuffer.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\CustomLightingToolEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\QuickPow.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\AmbientColorModuleEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\DynamicLightMapModuleEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\LuightLodMoudleEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\MeshCombineChangeNameWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\MeshCombineTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\SceneFogModuleEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\SceneToolEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\SceneToolListener.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SceneTool\SetCloudLightingModuleEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SHEncoding.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SHUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\SkyboxLightToolEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomLightingTool\Util.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\AssetBundleBuilder.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\ASTCSpritePackerPolicy.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\BaseBuildPacker.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\CustomSpritePackerPolicy.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\Etc2TextureBuildPacker.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\HdTextureBuildPacker.cs" />
    <Compile Include="Assets\Editor\Outsourced\CustomPackTexture\TexturePackTools.cs" />
    <Compile Include="Assets\Editor\Outsourced\DataBaseEditor\DataBaseEditorWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\DataGroupAssetEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\DataSheet\DataSheetEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\DataSheet\DataSheetModel.cs" />
    <Compile Include="Assets\Editor\Outsourced\DataSheet\DataSheetTableView.cs" />
    <Compile Include="Assets\Editor\Outsourced\DelegateInterface.cs" />
    <Compile Include="Assets\Editor\Outsourced\EditorLanguageController\EditorLanguageControl.cs" />
    <Compile Include="Assets\Editor\Outsourced\EditorUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\EditorWindowBaseEx.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Attributes\RangeWithoutClampingGammaAttribute.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Attributes\RangeWithoutClampingGammaDrawer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\BakingController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\ControllerBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\CullingController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\IController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\OptimizationController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\PreviewController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\SelectionController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\SessionController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\TransparencyController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\UVPackingController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Controllers\WorkflowController.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Events\EventDefinitions.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Events\EventSystem.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Events\IEventSystem.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Events\ResultsAppliedEvent.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Events\TextureBakedEvent.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\MeshMappingInfo.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\MeshProcessingData.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\MeshStatistics.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\ProcessingParameters.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\ProcessingResult.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\VertexClassificationResult.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Models\WorkSession.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Repository\IProcessingDataRepository.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Repository\ProcessingDataRepository.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\AtlasBakingPreparation.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\BoundaryVertexOptimizer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\CullingService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\EdgePairProcessor.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\IService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MaterialUpdateService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MeshCombineService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MeshOptimizationService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MeshSplitService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MeshStatisticsService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MeshSubdivisionService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MobileTextureBakingService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\MultiMaterialRenderService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\PreviewService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\QEMOptimizer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\QuadricErrorMatrix.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\RenderTextureManager.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\ServiceBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\TransparencyAnalysisService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\UVIslandDilationService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Services\UVUnwrapService.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\AssetPathUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\BarycentricWireframeUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\BufferResource.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\CustomMeshGenerator.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\DebugTools\MeshOptimizationDebugHelper.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\DependencyContainer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\DependencyInitializer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\EditorGUIHelper.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\MaterialResource.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\MeshPreviewUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\MeshResource.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\PathConstants.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\ResourceBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\ResourceManager.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\Texture2DResource.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\TextureReadableUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\TextureResource.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\TransparencyPrecisionUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utilities\TransparencyVisualizerUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Utils\MeshUtils.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\EditorWindowBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\IView.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\MeshOptimizerWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\PreviewRenderers\AtlasPreviewRenderer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\PreviewRenderers\MeshPreviewRenderer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\PreviewRenderers\OptimizationPreviewRenderer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\PreviewRenderers\PreviewRendererBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\PreviewRenderers\UVPreviewRenderer.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\UIComponents\OptimizationPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\UIComponents\PanelBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\UIComponents\SelectionPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\UIComponents\SubdivisionPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\UIComponents\TransparencyPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshOptimizer\Views\UIComponents\UVPackingPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\Editor\LODGeneratorHelperEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\Editor\SerializedPropertyExtensions.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\MSCore.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\MSEdge.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\MSFace.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\MSHalfEdge.cs" />
    <Compile Include="Assets\Editor\Outsourced\MeshSimplification\MSVertex.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelChecker.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelCheckerWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelFaceSeparate\ModelFaceSeparate.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportBakerConfig.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportBakerConfigInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportCore.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportLimitConfig.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportLimitConfigInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportLimitConfigWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportPipline.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportReportWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportSetting.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\Ahri_TailCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\BaseRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\CurveTimeRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\DeleteMoveAnimCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\DeleteMoveAnimCurveRuleWithoutRot.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\DeleteScaleCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\EnterUICamCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\FaceCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\Hurt02PosYCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\PosZCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\RootMotionNodeCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\RuleToolUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\ScaleCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderCurveRule\SmoothCurveRule.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportTeamLeaderTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelImportWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelResAsset.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelImport\ModelResMD5.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelOptimize\FbxOptimizeTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelOptimize\ModelOptimizeTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelSeparate\ModelAssetLinkerPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelSeparate\ModelSeparateTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelSeparate\ModelUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelSeparate\NXAssetLinker.cs" />
    <Compile Include="Assets\Editor\Outsourced\ModelSeparate\NXModelInfo.cs" />
    <Compile Include="Assets\Editor\Outsourced\ParticleSystemToGfxFramework\Scripts\Core\ConversionController.cs" />
    <Compile Include="Assets\Editor\Outsourced\ParticleSystemToGfxFramework\Scripts\UI\ParticleToGfxContextMenu.cs" />
    <Compile Include="Assets\Editor\Outsourced\ParticleSystemToGfxFramework\Scripts\Utils\ConversionLogger.cs" />
    <Compile Include="Assets\Editor\Outsourced\ParticleSystemToGfxFramework\Scripts\Utils\GfxFrameworkHelper.cs" />
    <Compile Include="Assets\Editor\Outsourced\PrefabAnimatorPlayer\PrefabAnimatorPlayerEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\PrefabInstance\CustomHierarchyIcons.cs" />
    <Compile Include="Assets\Editor\Outsourced\PrefabInstance\PrefabInstanceEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\PrefabInstance\PrefabPostprocess.cs" />
    <Compile Include="Assets\Editor\Outsourced\Render\VertexPainter\RxLookingGlass.cs" />
    <Compile Include="Assets\Editor\Outsourced\Render\VertexPainter\SVTXObjectEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\Render\VertexPainter\SVTXPainterWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\ScanToolEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapCameraAnimationInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditAreaPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditBasePage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditDynamicPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditEventPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditLogicPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditMainPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditPreviewPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigEditTopPage.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapConfigInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapManagerTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\BattleMapTriggerConfigInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\BattleMapTool\UtilFile.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\CircleCollider.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\ColliderBaseClass.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\ColliderFactory.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\ColliderWrapper.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\MutiTypeCollider.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\OctagonCollider.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\Collider\OnePoint.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\SDFColliderTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\SDFCoreTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\SDFTool\SDFPathTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\AnimationExporter.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\BindposeUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\BoneMappingUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\MeshProcessor.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\PrefabGenerator.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\ReplaceRuleUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\SharedSkinnedMeshToolData.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\SharedSkinnedMeshToolWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\Utils\LogUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\SharedSkinnedMeshTool\Utils\PathUtility.cs" />
    <Compile Include="Assets\Editor\Outsourced\SubPanelBase.cs" />
    <Compile Include="Assets\Editor\Outsourced\SVNTool\SelectionUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\SVNTool\SVNTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\AssetTreeView.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\EditorAnimatorInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\GetPrefabData.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\GetPrefabEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\MeshOcclusionBakeEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\RayCastSH.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\Editor\RenderImageToolEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\CommonShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\CustomPBRShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\DefaultShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\EyeShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\FXShaderGUI\EffectUberShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\InGameSceneShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\InGameShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\MaterialLightDir.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\MaterialLightDirScreenSpace.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\MaterialPropertyCommon.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\OutGameShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TATools\ShaderGUI\SceneShaderGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\LogicTriggerEditor\ChessPlayerLogicTriggerCommSubEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\LogicTriggerEditor\ChessPlayerLogicTriggerContentEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\LogicTriggerEditor\ChessPlayerLogicTriggerEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\LogicTriggerEditor\ChessPlayerLogicTriggerListSubEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\LogicTriggerEditor\ChessPlayerLogicTriggerUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\LogicTriggerEditor\ChessPlayerLogicTriggerVarSubEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderAllCfgGenerator.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderAnimatorParser.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderAnimatorRelax.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderAnimatorUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderCfgChange.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderConfigGenerator.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TeamLeaderEditor\TeamLeaderSelectAnimationStateEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\ChessAttackEditorWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\ChessAttackLineBindInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\ChessAttackSceneInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\ChessPlayerEffectBindLocInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\DoubleTinySceneConfigInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\LittleLegendCfgInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\LittleLegendPrefabGenTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\SubPanel\ChessAttackCommonPropertySubPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\SubPanel\ChessAttackCtrlSubPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\SubPanel\ChessAttackEffectListSubPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\SubPanel\ChessAttackEffectParamSubPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\SubPanel\ChessAttackSubPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\TinyEffectSceneControllerInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\TinyPreviewSceneEditorControllerInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\TinySceneCameraUIAdapterInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\TinySceneTimelineInspector.cs" />
    <Compile Include="Assets\Editor\Outsourced\TinyEffectScene\Editor\TkEditorScreenController.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\SVNWindow\SVNLogWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\SVNWindow\TKSvnCommitItem.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\SVNWindow\TKSvnCommitWindow.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKReference.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKReferenceData.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKShellCore.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnCore.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnFile.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnFileStatusCache.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnMainPanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnMergePanel.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnProjectWindowProcesser.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnSetting.cs" />
    <Compile Include="Assets\Editor\Outsourced\TKSvnTool\TKSvnUtil.cs" />
    <Compile Include="Assets\Editor\Outsourced\UIPreviewManager\UIPreviewManager.cs" />
    <Compile Include="Assets\Editor\Outsourced\UIPreviewManager\UIPreviewTreeView.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Attributes\ReorderableTableAttribute.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Attributes\TableAttribute.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Cells\ActionCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Cells\LabelCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Cells\ObjectCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Cells\PropertyCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Cells\TableCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\SelectFromCellTypeColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\SelectFromFunctionColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\SelectFromPropertyNameColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\SelectObjectReferenceColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\SelectorColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\TableColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\TableColumnEntry.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Columns\TableColumnOption.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Drawers\ReorderableTableDrawer.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Drawers\TableDrawer.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Tables\GUITable.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Tables\GUITableEntry.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Tables\GUITableLayout.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Tables\GUITableOption.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Tables\GUITableState.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Utils\GUIHelpers.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Utils\ReflectionHelpers.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\GUITable\Code\Editor\Utils\SerializationHelpers.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Attributes\ReorderableTableAttribute.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Attributes\TableAttribute.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Attributes\TableColumnAttribute.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\ActionCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\LabelCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\MultiActionCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\ObjectCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\PropertyCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\TableCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Cells\ToggleCell.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\SelectFromCellTypeColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\SelectFromFunctionColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\SelectFromPropertyNameColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\SelectObjectReferenceColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\SelectorColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\TableColumn.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\TableColumnEntry.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Columns\TableColumnOption.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Drawers\ReorderableTableDrawer.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Drawers\TableDrawer.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Tables\GUITable.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Tables\GUITableEntry.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Tables\GUITableLayout.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Tables\GUITableOption.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Tables\GUITableState.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Tables\TableItemData.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Utils\GUIHelpers.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Utils\ReflectionHelpers.cs" />
    <Compile Include="Assets\Editor\Outsourced\UITools\NXGUITable\Editor\Utils\SerializationHelpers.cs" />
    <Compile Include="Assets\Editor\Outsourced\UnitRendererManager\ACGameMaterialCurveEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\UnitRendererManager\ACGameMaterialCurveTestEditor.cs" />
    <Compile Include="Assets\Editor\Outsourced\UnitRendererManager\CHMCCfgGenerator.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\FBXAssetPostProcessor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\ModelPostProcess.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ASTC_AnalyseCSV.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ASTCBestOption.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ASTCExhaustiveGUI.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ASTCExhaustiveStatus.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ASTCExhaustiveTool.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ZgameLimitedSetting.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ZgameLimitedSettingMeta.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\TextureProcessor\ZgameLimitedTextureImportCheck.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\ZGameAssetPostProcessor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\ZGameMaterialProcessor.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\ZGameMenuTemplate.cs" />
    <Compile Include="Assets\Editor\Outsourced\ZGame\ZGameTextureProcessor.cs" />
    <Compile Include="Assets\Editor\Processor\ZgameLimitedAbName.cs" />
    <Compile Include="Assets\Editor\ProjectTool.cs" />
    <Compile Include="Assets\Editor\ScanTools\ScanSceneTool.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\KTAssetMemoryWindow.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\TKAssetGroupInspector.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\TKAssetGroupUtil.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\TKAssetLodCommand.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\TKAssetLodGenerator.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\TKAssetReportWindow.cs" />
    <Compile Include="Assets\Editor\TKAssetGroup\TKAssetTreeNode.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\ResourceCheckUtility.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\TextureUtil.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\TUTCE.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\TUTFuncLogger.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\TUTLog.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\TUTStartLogger.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Common\UnityUIUtility.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\ChangeFunctionSet.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\ComponentCopier.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\ErrorWindow.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\GlobalData.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\ObjectCopier.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\ObjectCopierWindow.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\PropertyNameModel.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ComponentCopy\SerializedDataModel.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Navigation\NavigationSelectionHistory.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Navigation\NavigationWindow.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\NavigatorTool\SelectionHistoryTool.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\NavigatorTool\SelectionHistoryWindow.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ResouceCheck\AudioCheck.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ResouceCheck\CheckWindow.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ResouceCheck\MeshCheck.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ResouceCheck\ShaderCheck.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\ResouceCheck\TextureCheck.cs" />
    <Compile Include="Assets\Editor\TUT\Editor\Search\SearchWindow.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassAnalyse.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassInfo\AllClassInfo.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassInfo\AllClassInfoManager.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassInfo\ClassInfo.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassInfo\ClassInfoUtil.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassInfo\JCEClassInfo.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\ClassInfo\PBClassInfo.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\ClassBasePoolFile.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ClassReadPool.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ClassWritePool.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\ClassBaseFile.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Collect\ClassCollect.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Equal\ClassEqual.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Hash\ClassHash.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Read\ClassRead.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Write\ClassWrite.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BaseGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BaseGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ArrayGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ArrayGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\AssetObjectGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\AssetObjectGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\BuildInClassGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\BuildInClassGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\BuildInGenericClassGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\CollectionGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\CollectionGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\CommonBuildInClass\CommonBuildInClassGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\CommonBuildInClass\CommonBuildInClassGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\ConfigCollection\ConfigCollectionGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\ConfigCollection\ConfigCollectionGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\BuildInStructGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\BuildInStructGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\KeyValuePairGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\KeyValuePairGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Enum\EnumGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Enum\EnumGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GeneratorConfigManager.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GeneratorManager.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GeneratorTransferType.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GenericClass\GenericClassGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GenericClass\GenericClassGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GenericStruct\GenericStructGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\GenericStruct\GenericStructGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Primitive\PrimitiveGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Primitive\PrimitiveGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\StructGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\StructGeneratorConfig.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\TypeGeneratorUtil.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\BaseUtilGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\EmptyReadUtilGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\ReadUtilGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\EmptyWriteUtiGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\WriteUtilGenerator.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilTool\AlignTool.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilTool\FixCharBinaryWriter.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilTool\JceConverter.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilTool\SplitContent.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilTool\StringTextWriter.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilTool\TwoIntKey.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoCollect.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoCollect_Config.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoEqual.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoEqual_Config.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoHash.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoHash_Config.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoWrite.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoWrite_Config.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\UniqueInfoWriteUtil.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\WriteUniqueInfo_ConfigPool.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\GenerateCode\ChessPlayerConfig_Gen\WriteUniqueInfo_Pool.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferBaseField_Equal.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferBaseField_Hash.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferBaseField_Write.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferClassField_Equal.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferClassField_Hash.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferClassField_Write.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferStructField_Equal.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferStructField_Hash.cs" />
    <Compile Include="Assets\Editor\UniqueInfoEditor\TransferField\TransferStructField_Write.cs" />
    <Compile Include="Assets\Editor\ZGame\ZGameEditorUtility.cs" />
    <Compile Include="Assets\Scripts\ChessBattle\FrameSync\Editor\FrameSyncEditorTools.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Asset Types\AnimationReferenceAssetEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Asset Types\SkeletonDataAssetInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Asset Types\SpineAtlasAssetInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\BoneFollowerGraphicInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\BoneFollowerInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\BoundingBoxFollowerInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\PointFollowerInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonGraphicInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonMecanimInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonRendererCustomMaterialsInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonRendererInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonUtilityBoneInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Components\SkeletonUtilityInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Menus.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Shaders\SpineSpriteShaderGUI.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\SpineAttributeDrawers.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\AssetDatabaseAvailabilityDetector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\AssetUtility.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\BuildSettings.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\DataReloadHandler.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\Icons.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\Instantiation.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\Preferences.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\SpineEditorUtilities.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\SpineHandles.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\SpineInspectorUtility.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Utility\SpineMaskUtilities.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Windows\SkeletonBaker.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Windows\SkeletonBakingWindow.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Windows\SkeletonDebugWindow.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Windows\SpinePreferences.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Modules\SkeletonRenderSeparator\Editor\SkeletonPartsRendererInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Modules\SkeletonRenderSeparator\Editor\SkeletonRenderSeparatorInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Modules\SlotBlendModes\Editor\SlotBlendModesEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineAnimationStateClipInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineAnimationStateDrawer.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineAnimationStateGraphicTrackInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineAnimationStateTrackInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineSkeletonFlipClipEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineSkeletonFlipClipInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineSkeletonFlipDrawer.cs" />
    <Compile Include="Assets\Scripts\Outsource\Plugins\Spine_timeline\Editor\SpineSkeletonFlipTrackInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\EffectFramework\Editor\EffectLodTreeViewEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\EffectFramework\Editor\EffectMaterialScriptEd.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\EffectFramework\Editor\EffectPrefabProcessor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\EffectFramework\Editor\ParticleSystemLodLevelInspector.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\GfxAdapter\Editor\SpringTrailBezierBuilderEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\GfxAdapter\Editor\SpringTrailRendererEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\GfxAdapter\Editor\TKTrailEditor.cs" />
    <Compile Include="Assets\Scripts\Outsource\Render\Effect\Support\Editor\SortingLayerDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ApplyPrefabChanges.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\AssetContainerHandler.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\AssetIEnumerator.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\AssetProcessUtils.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\AtlasViewer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\CustomizeDllUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\CircleLayoutGroupInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\GraphicTextEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\HyperTextEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\MeshRendererExtendEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\MixTextEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\NonDrawingGraphicEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\ScriptableTableEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\ScriptableTableEditorData.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\SkinnedMeshRendererExtendEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\TKTextEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExtendEditor\TrailRendererExtendEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ExternalPrefabHandler.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FileSystemUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\EditorHelper.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\EditorUtils.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\Font\ArtistFont.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\Font\BetterList.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\Font\BMFont.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\Font\BMFontReader.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\Font\BMGlyph.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\Font\ByteReader.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\FontGenerator\FontMakerWizard.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\MediaTools\MediaEncoder.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\MediaTools\MediaScanner.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\PrefabReferenceCheck.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ReferenceFinder\ReferenceFinder.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ReferenceFinder\ReferenceFinderData.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ReferenceFinder\ReferenceFinderDataPostProcessor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ReferenceFinder\RipGrepHelper.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\AssetbundlesMenuItems.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\BuildAssetDatabase.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\BuildAssetMemoryUsage.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\BuildPreloadShader.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\BuildScript.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\CheckCycleDependence.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ScriptsForAssetBundleSystem\Editor\SymlinkUtility.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\ShaderInvalidKeywordSearcher\ShaderInvalidKeywordSearcher.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\BlurFrontGroundEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\DataBinding\BinderEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\DataBinding\BindingContextEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\DataBinding\EditorMembersHelper.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\DataBinding\TKBinderEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\DataBinding\TKCammandBinderEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKEditor\TKFrameModeManager.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\TKUIControlsCreateor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\Utilities\ASTCUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\Utilities\CheckUpdateResTool.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\Utilities\CheckUpdateResToolWindow.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Editor\Utilities\GUIDPathDataCache.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ArtCode\EffectStack\Editor\TKPostProcessingStackEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\Extensions\Art_Tools\Editor\CleanupFXAssets.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\Extensions\Art_Tools\Editor\ScanEffectPhysics.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\Extensions\Art_Tools\Editor\SetShaderLOD.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\H_Img\Editor\H_Img\AlphaImage.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\H_Img\Editor\H_Img\DiffImage.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\H_Img\Editor\H_Img\H_Img_Creater.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\AlphaSplit\SplitUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\ComponentReplaceUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\ImageAlphaUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\ImageExMenu.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\ImageUVEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\TKSplitAlphaUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\ImageEx\Editor\UGUIReplaceUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\NTweenEditorTools.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenAlphaEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenAlphaExtendEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenAnchoredPositionEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenCanvasAlphaEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenColorEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenPositionEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenRotationEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenScaleEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\TweenTransformEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\NTween\Editor\UITweenerEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\TKPlugins\Extensions\Editor\DataAssetEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Plugins\TKPlugins\Sound\Editor\AudioPlayEditor.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxAnimation\GfxActiveModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxAnimation\GfxAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxAnimation\GfxModuleBaseDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxAnimation\GfxRotModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxAnimation\GfxScaleModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxAnimation\GfxTranslateModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxCurveDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxEditorUtility.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxGhostShadow\GfxGhostShadowInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxGlobalShader\GfxGlobalShaderAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxGlobalShader\GfxSceneAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxLineAnimation\GfxLineAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxLodParticleSystem\GfxLodParticleSystemInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxLodParticleSystem\GfxLodParticleSystemParamDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMaterial\GfxMaterialAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMaterial\GfxMaterialColorCurveDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMaterial\GfxMaterialFloatCurveDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMaterial\GfxMaterialInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMaterial\GfxMaterialTextureCurveDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMaterial\GfxMaterialVector4CurveDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxMoveTestDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxParticleSystem\GfxParticleSystemInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxPreviewView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxProfiler\GfxClipProfilerTable.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxProfiler\GfxMeshProfilerTable.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxProfiler\GfxProfilerCore.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxProfiler\GfxProfilerFrameData.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxProfiler\GfxProfilerWindow.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxProfiler\GfxTextureProfilerTable.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRandom\GfxActiveRandomModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRandom\GfxPositionRandomModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRandom\GfxRandomAnimationInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRandom\GfxRandomDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRandom\GfxRandomModuleBaseDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRandom\GfxRotationRandomModuleDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRootInspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRootLodView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRootProfiler.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxRootTreeView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxSystemTimeControl.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxTagSelectWindow.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxTimeDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\GfxUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\Tool\GfxDrawcallTool.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\Tool\GfxTextureCombineTool.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\EffectProfilerWindow.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerEffectAnalyzerView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerMapView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerPoolView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerTableTreeView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerTotalDataView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\EffectProfilerRuntime\Editor\EffectProfiler\ProfilerUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GfxTextureProfiler\Editor\GfxTexture.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GfxTextureProfiler\Editor\GfxTextureProfiler.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Cells\ActionCell.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Cells\ImageCell.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Cells\LabelCell.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Cells\ObjectCell.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Cells\PropertyCell.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Cells\TableCell.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\SelectFromCellTypeColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\SelectFromFunctionColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\SelectFromPropertyNameColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\SelectObjectReferenceColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\SelectorColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\TableColumn.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\TableColumnEntry.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Columns\TableColumnOption.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Drawers\ReorderableTableDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Drawers\TableDrawer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Tables\GUITable.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Tables\GUITableEntry.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Tables\GUITableLayout.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Tables\GUITableOption.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Tables\GUITableState.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Utils\GUIHelpers.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Utils\ReflectionHelpers.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Tool\GUITable\Code\Editor\Utils\SerializationHelpers.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxAnimation_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxGlobalShaderAnimation_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxLineAnimation_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxLodParticleSystem_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxMaterialAnimation_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxParticleSystem_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxRandomAnimation_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxRoot_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxSceneAnimation_Unity_Inspector.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Unity\Editor\GfxUnityUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\CustomProfiler\Model\EditorFrameStats.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\CustomProfiler\Model\ProfilerFrameModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\CustomProfiler\Model\ProfilerLineDataGroup.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\CustomProfiler\View\ProfilerFrameGraph.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\CustomProfiler\View\ProfilerTreeView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\DecodeDataThread.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\JobList.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\MethodCallNodePool.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\MethodCallStackTree.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\MethodProfilerModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\MethodProfilerType.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\MultiThreadLoadMethodData.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\ProfilerFrameStatus.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\Model\SingleThreadLoadData.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\View\MethodTreeModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\View\MethodTreeView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\DeepProfiler\View\ProfilerMethodGraph.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\HistoryPanel\HistoryPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\HistoryPanel\HistoryTreeView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\HistoryPanel\Model\HistoryModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\HistoryPanel\Model\HistoryTreeModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\ClassDataModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MethodDataModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoCallStackTree.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoDataModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoDataType.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoDetailFrameStatus.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoFrameStatus.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoStatus.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\Model\MonoThreadStatus.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\View\MonoTreeModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\View\MonoTreeView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\View\ProfilerMonoGraph.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\MonoProfiler\View\ThreadMonoGraph.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\PointProfiler\Model\ProfilerPointModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\PointProfiler\View\OnePointView.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\PointProfiler\View\ProfilerPointGraph.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerConfig.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerDrawLine\Model\ProfilerLineData.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerDrawLine\Model\ProfilerLineModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerDrawLine\View\ProfilerFrameLine.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerDrawLine\View\ProfilerLineDraw.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerModel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerPanel\ProfileDetailPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerPanel\ProfilerChosenPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerPanel\ProfilerGraphPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerPanel\ProfilerMainPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerPanel\ProfilerSummaryPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerPanel\ProfilerTopBar.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\ProfilerWindow.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\SaveProfiler\NetClient.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\SaveProfiler\SaveProfilerPanel.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\AtomicLock.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\BinaryReaderExtend.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\EditorReadThread.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\EditorWriteThread.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\FixRingBuffer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\GCUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\HorizontalSplitter.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\IModelInterface.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\IProfilerGraphInterface.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\MergeSortUtil.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\QueueRingBuffer.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\Splitter.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\TreeElement.cs" />
    <Compile Include="Assets\Scripts\TKFramework\Scripts\TKFrame\Profiler\LightProfiler\Editor\Util\VerticalSplitter.cs" />
    <Compile Include="Assets\Shaders\OutGame\UniqueShadow\Editor\ShadowEditor.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkAmbientInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkAudioListenerInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkBankInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkBaseInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkEnvironmentInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkEnvironmentPortalInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkEventInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkEventPlayableInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkGameObjectInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkGameObjListenerListDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkPortalManager.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkRoomInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkRoomPortalInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkRTPCPlayableInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkSpatialAudioListenerInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkStateInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkSwitchInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseComponents\AkUnityEventHandlerInspector.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\Android\AkWwiseMenu_Android.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\Common\AkUnityBuilderBase.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\Common\AkUnityPluginInstallerBase.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\Common\AkWwiseIdConverter.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\iOS\AkWwiseMenu_iOS.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\Mac\AkWwiseMenu_Mac.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseMenu\Windows\AkWwiseMenu_Windows.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkBuildPreprocessor.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkPluginActivator.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkUnitySettingsParser.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkWSAUtils.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkWwisePostImportCallbackSetup.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkWwiseSettingsWindow.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkWwiseSetupWizard.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseSetupWizard\AkXboxOneUtils.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\AcousticTextureDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\AuxBusDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\BankDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\BaseTypeDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\EventDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\RTPCDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\StateDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\SwitchDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseTypes\TriggerDrawer.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseBankInfoBuilder.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseComponentPicker.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseCosSetting.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwisePicker.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseProjectData.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseProjectInfo.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseSoundScan.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseTreeView.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseWWUBuilder.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\AkWwiseXMLBuilder.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\TreeViewControl\TreeViewControl.cs" />
    <Compile Include="Assets\Wwise\Editor\WwiseWindows\TreeViewControl\TreeViewItem.cs" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\EmptyWriteUtilContentTemplate.txt" />
    <None Include="Assets\Scripts\Outsource\Plugins\Spine\Editor\spine-unity\Editor\Resources\SpineAssetDatabaseMarker.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassReadConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ConfigPool\ClassWriteConfigOneFixPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionReadConfigClassTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\Render\VertexPainter\Shaders\VertexColorChannelDisplay.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassWriteClassTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\VertexColorShader.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ConfigPool\ClassReadConfigDataPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayReadClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassCollectRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayHashRankTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ConfigPool\ClassWriteOnePoolWithChildTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Collect\ClassCollectTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ConfigPool\ClassWriteConfigPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\WriteUtilAssetContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassWriteRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayWriteConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectEqualRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayEqualRankTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Enum\ClassTemplate\ClassReadClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassWriteConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ClassWriteDataPoolTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\UVMask.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ClassWriteAssetPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Enum\ClassTemplate\ClassReadRawClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ClassReadPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ChildRefTemplate\ChildClassCollectRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ClassWriteOnePoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ChildRefTemplate\ChildClassEqualRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectHashRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayReadRankTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\WriteUtilTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassCollectRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ConfigPool\ClassReadConfigOneUnFixPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayHashClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayCollectClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassHashRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\RefTemplate\StructHashRefTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\WireframeDoubleSided.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayReadConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassHashRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassReadClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\RefTemplate\StructReadConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\RefTemplate\StructEqualRefTemplate.txt" />
    <None Include="Assets\Scripts\TKFramework\Scripts\TKFrame\extension\GfxFramework\Core\Editor\OverDraw.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ClassReadAssetPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassWriteRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayReadConfigRankTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassWriteConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassReadConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Hash\ClassHashTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\ReadUtilAssetContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectWriteConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassWriteConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ClassReadDataPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassReadConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ConfigPool\ClassReadConfigPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassCollectClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\ClassTemplate\KeyValuePairHashClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Equal\ClassEqualTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\ClassTemplate\StructEqualClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ClassWritePoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\ReadUtilTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\TextureBaking.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ChildRefTemplate\ChildClassWriteRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayWriteRankTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\ClassTemplate\StructHashClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\RefTemplate\StructReadRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\ConfigCollection\ClassTemplate\ConfigCollectionReadConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectReadConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ConfigPool\ClassWriteConfigOneUnFixPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\ClassTemplate\StructReadClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\RefTemplate\StructWriteRefTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\TriangleAlphaVisualizerMobile.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionWriteConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\ReadUtilContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ChildRefTemplate\ChildClassHashRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionReadClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectWriteRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionWriteClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\CommonBuildInClass\ClassTemplate\CommonBuildInClassHashClassTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\TriangleAlphaVisualizer.shader" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\ComputeShaders\TransparencyAnalysis.compute" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Read\ClassReadTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\WriteUtilContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\ClassTemplate\KeyValuePairCollectClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectCollectRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassHashClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\RefTemplate\StructCollectRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ClassReadOnePoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassEqualRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\AssetObject\RefTemplate\AssetObjectReadRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Write\WriteUtilConfigContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\ClassTemplate\StructCollectClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassEqualRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\ClassTemplate\KeyValuePairReadConfigClassTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\WireframeBarycentric.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionEqualClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionCollectClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\ClassTemplate\KeyValuePairReadClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayCollectRankTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ClassTemplate\ClassEqualClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\ReadUtilConfigContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ConfigPool\ClassReadOnePoolWithChildTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayWriteClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\CommonBuildInClass\ClassTemplate\CommonBuildInClassEqualClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\ClassTemplate\KeyValuePairWriteClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInStruct\KeyValuePair\ClassTemplate\KeyValuePairEqualClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\CommonBuildInClass\RefTemplate\CommonBuildInClassCollectRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\UtilGenerator\Read\EmptyReadUtilContentTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ChildRefTemplate\ChildClassWriteConfigRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Read\ConfigPool\ClassReadConfigOneFixPoolTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\ChildRefTemplate\ChildClassReadRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\RefTemplate\BuildInClassReadRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\ClassTemplate\StructReadConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Class\RefTemplate\ClassReadRefTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Collection\ClassTemplate\CollectionHashClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\PoolGenerator\Write\ConfigPool\ClassWriteConfigDataPoolTemplate.txt" />
    <None Include="Assets\Editor\Outsourced\MeshOptimizer\Resources\Shaders\UVIslandDilation.shader" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\ConfigCollection\ClassTemplate\ConfigCollectionWriteConfigClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TransferGenerator\Write\ClassWriteTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\Struct\ClassTemplate\StructWriteClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\ClassTemplate\ArrayEqualClassTemplate.txt" />
    <None Include="Assets\Editor\UniqueInfoEditor\AnalyseClass\TypeGenerator\BuildInClass\Array\RankTemplate\ArrayWriteConfigRankTemplate.txt" />
    <Reference Include="Unity.Subsystem.Registration">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.Subsystem.Registration.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.EditorCoroutines.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AdaptivePerformance">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.AdaptivePerformance.dll</HintPath>
    </Reference>
    <Reference Include="Unity.PackageManagerUI.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.PackageManagerUI.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.MemoryProfiler">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.MemoryProfiler.dll</HintPath>
    </Reference>
    <Reference Include="Unity.CollabProxy.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AdaptivePerformance.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.AdaptivePerformance.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Performance.Profile-Analyzer.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Analytics.DataPrivacy">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.Analytics.DataPrivacy.dll</HintPath>
    </Reference>
    <Reference Include="Unity.MemoryProfiler.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/ScriptAssemblies/Unity.MemoryProfiler.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.BaselibModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.BaselibModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.FileSystemHttpModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.FileSystemHttpModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpatialTrackingModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpatialTrackingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StyleSheetsModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.StyleSheetsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TimelineModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.TimelineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Locator">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/Unity.Locator.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/GUISystem/UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/GUISystem/Editor/UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TestRunner">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/TestRunner/Editor/UnityEditor.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TestRunner">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/TestRunner/UnityEngine.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/TestRunner/net35/unity-custom/nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Timeline">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/Timeline/RuntimeEditor/UnityEngine.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Timeline">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/Timeline/Editor/UnityEditor.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Networking">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/Networking/UnityEngine.Networking.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Networking">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/Networking/Editor/UnityEditor.Networking.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GoogleAudioSpatializer">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/UnityGoogleAudioSpatializer/Editor/UnityEditor.GoogleAudioSpatializer.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GoogleAudioSpatializer">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/UnityGoogleAudioSpatializer/RuntimeEditor/UnityEngine.GoogleAudioSpatializer.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpatialTracking">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/UnitySpatialTracking/Editor/UnityEditor.SpatialTracking.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpatialTracking">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/UnitySpatialTracking/RuntimeEditor/UnityEngine.SpatialTracking.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VR">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/UnityExtensions/Unity/UnityVR/Editor/UnityEditor.VR.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/Managed/UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/PlaybackEngines/windowsstandalonesupport/UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>D:/svn-JK_TFT_other/Assets/BattlePluginCore/Plugins/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>D:/svn-JK_TFT_other/Assets/BattlePluginCore/Plugins/protobuf-net2.3.10/protobuf-net.dll</HintPath>
    </Reference>
    <Reference Include="LitJson">
      <HintPath>D:/svn-JK_TFT_other/Assets/BattlePluginCore/Plugins/TKPlugins/Json/LitJson.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>D:/svn-JK_TFT_other/Assets/Extensions/DOTween/DOTween.dll</HintPath>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>D:/svn-JK_TFT_other/Assets/Extensions/DOTween/Editor/DOTweenEditor.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>D:/svn-JK_TFT_other/Assets/Plugins/ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="JetBrains.Rider.Unity.Editor.Plugin.Repacked">
      <HintPath>D:/svn-JK_TFT_other/Assets/Plugins/Editor/JetBrains/JetBrains.Rider.Unity.Editor.Plugin.Repacked.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Data.Sqlite">
      <HintPath>D:/svn-JK_TFT_other/Assets/Plugins/sqllite/Mono.Data.Sqlite.dll</HintPath>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>D:/svn-JK_TFT_other/Assets/Scripts/TKFramework/Plugins/TKPlugins/QRCode/zxing.unity.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>D:/svn-JK_TFT_other/Assets/ThirdParty_Plugins/Common/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>D:/svn-JK_TFT_other/Assets/ThirdParty_Plugins/Common/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>D:/svn-JK_TFT_other/Assets/ThirdParty_Plugins/Common/Plugins/Sirenix/Assemblies/Sirenix.Serialization.Config.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>D:/svn-JK_TFT_other/Assets/ThirdParty_Plugins/Common/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>D:/svn-JK_TFT_other/Assets/ThirdParty_Plugins/Common/Plugins/Sirenix/Assemblies/Sirenix.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>D:/svn-JK_TFT_other/Assets/ThirdParty_Plugins/Common/Plugins/Sirenix/Assemblies/Sirenix.Utilities.Editor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Advertisements">
      <HintPath>D:/svn-JK_TFT_other/Library/PackageCache/com.unity.ads@2.0.8/Editor/UnityEditor.Advertisements.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Analytics.StandardEvents">
      <HintPath>D:/svn-JK_TFT_other/Library/PackageCache/com.unity.analytics@3.2.3/AnalyticsStandardEvents/Unity.Analytics.StandardEvents.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Analytics.Editor">
      <HintPath>D:/svn-JK_TFT_other/Library/PackageCache/com.unity.analytics@3.2.3/Editor/Unity.Analytics.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Analytics.Tracker">
      <HintPath>D:/svn-JK_TFT_other/Library/PackageCache/com.unity.analytics@3.2.3/Editor/Unity.Analytics.Tracker.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Unsafe">
      <HintPath>D:/svn-JK_TFT_other/Packages/com.unity.burst/Unity.Burst.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil">
      <HintPath>D:/svn-JK_TFT_other/Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Mdb">
      <HintPath>D:/svn-JK_TFT_other/Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Pdb">
      <HintPath>D:/svn-JK_TFT_other/Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Cecil.Rocks">
      <HintPath>D:/svn-JK_TFT_other/Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Purchasing">
      <HintPath>D:/svn-JK_TFT_other/Library/PackageCache/com.unity.purchasing@2.0.3/Editor/UnityEditor.Purchasing.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="UnityScript">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.dll</HintPath>
    </Reference>
    <Reference Include="UnityScript.Lang">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.Lang.dll</HintPath>
    </Reference>
    <Reference Include="Boo.Lang">
      <HintPath>C:/Program Files/Unity/Hub/Editor/2018.4.25f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/Boo.Lang.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp.csproj">
      <Project>{C73B7391-64B5-3EC1-64A2-1B4B0E8FBC46}</Project>
      <Name>Assembly-CSharp</Name>
    </ProjectReference>
    <ProjectReference Include="Cinemachine.csproj">
      <Project>{DF0C6492-22B9-0CC5-FF0C-34F8C4529FBF}</Project>
      <Name>Cinemachine</Name>
    </ProjectReference>
    <ProjectReference Include="Sirenix.OdinInspector.Modules.UnityMathematics.csproj">
      <Project>{927C39A5-0FE2-FD20-C4D7-4862921BE75E}</Project>
      <Name>Sirenix.OdinInspector.Modules.UnityMathematics</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Burst.csproj">
      <Project>{8B917C0B-ACDC-D4C1-A200-F0BE55E6E3D6}</Project>
      <Name>Unity.Burst</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Burst.Editor.csproj">
      <Project>{8AA13396-CC50-CC0C-82B1-BC09457E8550}</Project>
      <Name>Unity.Burst.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.csproj">
      <Project>{861D9B84-F144-C0DC-5359-036F64873A04}</Project>
      <Name>Unity.Mathematics</Name>
    </ProjectReference>
    <ProjectReference Include="SystemConfig.csproj">
      <Project>{7CA7654B-1713-D7AC-21E6-B33BD67BE9D6}</Project>
      <Name>SystemConfig</Name>
    </ProjectReference>
    <ProjectReference Include="Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj">
      <Project>{7C2310A1-3FB0-3D79-BEB6-0B022DD79186}</Project>
      <Name>Sirenix.OdinInspector.CompatibilityLayer.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="com.unity.cinemachine.editor.csproj">
      <Project>{C1F8CC89-2B5E-5D1C-6C87-37A9F2A44544}</Project>
      <Name>com.unity.cinemachine.editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.Editor.csproj">
      <Project>{3A13EE93-EAA9-1AD7-9444-1936BC523E71}</Project>
      <Name>Unity.Mathematics.Editor</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>