using UnityEngine;
using UnityEditor;
using ParticleSystemToGfxFramework.Data;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// 配置文件生成器，用于创建默认的验证配置
    /// </summary>
    public static class ConfigGenerator
    {
        private const string CONFIG_PATH = "Assets/Editor/Outsourced/ParticleSystemToGfxFramework/Config/DefaultConversionRules.asset";
        
        /// <summary>
        /// 创建默认验证配置文件
        /// </summary>
        [MenuItem("TATools/粒子转Gfx/创建默认验证配置")]
        public static void CreateDefaultValidationConfig()
        {
            // 检查是否已存在配置文件
            var existingConfig = AssetDatabase.LoadAssetAtPath<ValidationConfig>(CONFIG_PATH);
            if (existingConfig != null)
            {
                bool overwrite = EditorUtility.DisplayDialog(
                    "配置文件已存在",
                    "默认验证配置文件已存在，是否覆盖？",
                    "覆盖", "取消"
                );
                
                if (!overwrite)
                {
                    ConversionLogger.LogInfo("取消创建默认配置文件");
                    return;
                }
            }
            
            try
            {
                // 创建默认配置
                var config = ValidationConfig.CreateDefault();
                
                // 确保目录存在
                var directory = System.IO.Path.GetDirectoryName(CONFIG_PATH);
                if (!System.IO.Directory.Exists(directory))
                {
                    System.IO.Directory.CreateDirectory(directory);
                }
                
                // 保存配置文件
                AssetDatabase.CreateAsset(config, CONFIG_PATH);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                // 选中新创建的配置文件
                Selection.activeObject = config;
                EditorGUIUtility.PingObject(config);
                
                ConversionLogger.LogInfo($"默认验证配置文件已创建: {CONFIG_PATH}");
                ConversionLogger.LogInfo($"配置包含 {config.GetTotalRuleCount()} 个验证规则");
                
                // 显示配置详情
                LogConfigDetails(config);
            }
            catch (System.Exception ex)
            {
                ConversionLogger.LogError("创建默认配置文件失败", ex);
            }
        }
        
        /// <summary>
        /// 加载默认验证配置
        /// </summary>
        public static ValidationConfig LoadDefaultConfig()
        {
            var config = AssetDatabase.LoadAssetAtPath<ValidationConfig>(CONFIG_PATH);
            
            if (config == null)
            {
                ConversionLogger.LogWarning("默认验证配置文件不存在，请先创建");
                return null;
            }
            
            if (!config.IsValid())
            {
                ConversionLogger.LogError("验证配置文件无效");
                return null;
            }
            
            return config;
        }
        
        /// <summary>
        /// 验证配置文件是否存在
        /// </summary>
        public static bool HasDefaultConfig()
        {
            return AssetDatabase.LoadAssetAtPath<ValidationConfig>(CONFIG_PATH) != null;
        }
        
        /// <summary>
        /// 记录配置详情
        /// </summary>
        private static void LogConfigDetails(ValidationConfig config)
        {
            ConversionLogger.LogInfo("=== 验证配置详情 ===");
            ConversionLogger.LogInfo($"版本: {config.Version}");
            ConversionLogger.LogInfo($"描述: {config.Description}");
            
            ConversionLogger.LogInfo($"基础属性规则 ({config.BasicRules.Length}个):");
            foreach (var rule in config.BasicRules)
            {
                ConversionLogger.LogInfo($"  - {rule.name}: {rule.propertyPath} {rule.comparisonOperator} {rule.expectedValue}");
            }
            
            ConversionLogger.LogInfo($"模块状态规则 ({config.ModuleRules.Length}个):");
            foreach (var rule in config.ModuleRules)
            {
                ConversionLogger.LogInfo($"  - {rule.name}: {rule.moduleNames.Length}个模块, 允许启用: {rule.allowEnabled}");
            }
            
            ConversionLogger.LogInfo($"表达式规则 ({config.ExpressionRules.Length}个):");
            foreach (var rule in config.ExpressionRules)
            {
                ConversionLogger.LogInfo($"  - {rule.name}: {rule.expression}");
            }
        }
        
        /// <summary>
        /// 打开配置文件所在目录
        /// </summary>
        [MenuItem("TATools/粒子转Gfx/打开配置目录")]
        public static void OpenConfigDirectory()
        {
            var directory = System.IO.Path.GetDirectoryName(CONFIG_PATH);
            if (System.IO.Directory.Exists(directory))
            {
                EditorUtility.RevealInFinder(directory);
            }
            else
            {
                ConversionLogger.LogWarning("配置目录不存在");
            }
        }
    }
}
