# ParticleSystemToGfxFramework 开发任务清单

## 📋 项目概述

基于PRD中的程序架构和数据结构设计，将开发任务按照依赖关系和优先级进行分解，确保每个任务只修改一个功能点，便于实现和测试。

## 🎯 开发策略

- **自底向上**：先实现基础数据结构和工具类，再实现业务逻辑
- **分层开发**：按照Data → Utils → Core → UI的顺序开发
- **测试驱动**：每个功能模块都要有对应的单元测试
- **增量验证**：每完成一个模块就进行编译验证

## 📊 任务分类和优先级

### 🟢 P0 - 核心基础 (必须完成)
- 数据结构定义
- 基础工具类
- 核心转换逻辑

### 🟡 P1 - 主要功能 (重要)
- 用户界面
- 错误处理
- 日志记录

### 🔵 P2 - 增强功能 (可选)
- 单元测试
- 性能优化
- 文档完善

---

## 📝 详细任务清单

### 阶段1：基础数据结构 (P0)

#### 任务1.1：创建基础数据模型
- [ ] 创建 `ConversionSettings.cs` - 转换设置配置
- [ ] 创建 `ConversionRequest.cs` - 转换请求数据结构
- [ ] 创建 `ConversionResult.cs` - 转换结果数据结构
- [ ] 创建 `ConversionStatistics.cs` - 转换统计信息
- [ ] 创建 `ConversionError.cs` 和 `ConversionWarning.cs` - 错误和警告信息

**注意事项**：
- 所有数据结构使用 `readonly struct` 确保不可变性
- 在构造函数中进行参数验证
- 添加详细的XML文档注释

#### 任务1.2：创建粒子系统信息数据结构
- [ ] 创建 `ParticleSystemInfo.cs` - 粒子系统信息主结构
- [ ] 创建 `RenderingInfo.cs` - 渲染信息结构
- [ ] 创建 `AnimationInfo.cs` - 动画信息结构
- [ ] 创建 `ModuleStates.cs` - 模块状态信息
- [ ] 创建动画模块信息结构：
  - [ ] `ColorOverLifetimeInfo.cs`
  - [ ] `RotationOverLifetimeInfo.cs`
  - [ ] `SizeOverLifetimeInfo.cs`

**注意事项**：
- 确保从ParticleSystem正确提取所有必要信息
- 处理可能的null值和异常情况
- 验证数据提取的准确性

#### 任务1.3：创建Gfx组件数据结构
- [ ] 创建 `GfxComponentData.cs` - 抽象基类
- [ ] 创建 `GfxAnimationData.cs` - GfxAnimation组件数据
- [ ] 创建 `GfxMaterialAnimationData.cs` - GfxMaterialAnimation组件数据
- [ ] 创建 `MeshComponentData.cs` - Mesh组件数据
- [ ] 创建时间和曲线数据结构：
  - [ ] `GfxTimeData.cs`
  - [ ] `GfxCurveData.cs`
  - [ ] `GfxMaterialColorCurveData.cs`

**注意事项**：
- 确保与GfxFramework的数据结构兼容
- 正确映射ParticleSystem属性到Gfx属性
- 处理数据类型转换

#### 任务1.4：创建Gfx模块配置数据结构
- [ ] 创建 `GfxActiveModuleData.cs` - 显示控制模块数据
- [ ] 创建 `GfxRotModuleData.cs` - 旋转模块数据
- [ ] 创建 `GfxScaleModuleData.cs` - 缩放模块数据

**注意事项**：
- 确保模块数据结构与GfxFramework一致
- 提供合理的默认值
- 支持模块的启用/禁用状态

### 阶段2：工具类和验证器 (P0)

#### 任务2.1：创建粒子系统验证器
- [ ] 创建 `ParticleSystemValidator.cs`
- [ ] 实现 `IsValidForConversion()` 方法
- [ ] 实现 `ValidateMaxParticles()` 方法
- [ ] 实现 `ValidateRenderMode()` 方法
- [ ] 实现 `ValidateForbiddenModules()` 方法

**注意事项**：
- 严格按照PRD中的筛选条件实现验证逻辑
- 提供详细的验证失败原因
- 确保验证逻辑的准确性

#### 任务2.2：创建GfxFramework辅助类
- [ ] 创建 `GfxFrameworkHelper.cs`
- [ ] 实现 `HasGfxRoot()` 方法 - 检查是否有GfxRoot_Unity组件
- [ ] 实现 `GetGfxRoot()` 方法 - 获取GfxRoot_Unity组件
- [ ] 实现 `ValidateGfxFrameworkDependencies()` 方法

**注意事项**：
- 确保正确检测GfxFramework组件
- 处理组件不存在的情况
- 提供清晰的错误信息

#### 任务2.3：创建转换日志记录器
- [ ] 创建 `ConversionLogger.cs`
- [ ] 实现不同级别的日志方法：`LogInfo()`, `LogWarning()`, `LogError()`
- [ ] 实现日志格式化功能
- [ ] 实现转换统计信息记录

**注意事项**：
- 使用Unity的Debug.Log系列方法
- 提供结构化的日志输出
- 支持不同的日志级别控制

### 阶段3：核心转换逻辑 (P0)

#### 任务3.1：创建粒子系统分析器
- [ ] 创建 `ParticleSystemAnalyzer.cs`
- [ ] 实现 `AnalyzePrefab()` 方法 - 分析Prefab中的所有粒子系统
- [ ] 实现 `FilterValidParticleSystems()` 方法 - 筛选符合条件的粒子系统
- [ ] 实现 `ExtractParticleSystemProperties()` 方法 - 提取粒子系统属性

**注意事项**：
- 正确遍历Prefab中的所有ParticleSystem组件
- 使用ParticleSystemValidator进行验证
- 创建不可变的ParticleSystemInfo对象

#### 任务3.2：创建属性映射器
- [ ] 创建 `PropertyMapper.cs`
- [ ] 实现 `MapToGfxComponents()` 方法 - 主映射方法
- [ ] 实现 `MapToGfxAnimation()` 方法 - 映射到GfxAnimation
- [ ] 实现 `MapToGfxMaterialAnimation()` 方法 - 映射到GfxMaterialAnimation
- [ ] 实现 `MapToMeshComponents()` 方法 - 映射到Mesh组件
- [ ] 实现时间和曲线映射方法：
  - [ ] `MapTimeData()` - 映射时间数据
  - [ ] `MapRotationModule()` - 映射旋转模块
  - [ ] `MapScaleModule()` - 映射缩放模块
  - [ ] `MapColorCurves()` - 映射颜色曲线

**注意事项**：
- 严格按照PRD中的映射关系实现
- 正确处理ParticleSystem.MinMaxCurve到AnimationCurve的转换
- 处理可选模块的映射逻辑

#### 任务3.3：创建Gfx组件工厂
- [ ] 创建 `GfxComponentFactory.cs`
- [ ] 实现 `CreateComponents()` 方法 - 主创建方法
- [ ] 实现 `CreateGfxAnimation()` 方法 - 创建GfxAnimation组件
- [ ] 实现 `CreateGfxMaterialAnimation()` 方法 - 创建GfxMaterialAnimation组件
- [ ] 实现 `CreateMeshComponents()` 方法 - 创建Mesh组件
- [ ] 实现模块配置方法：
  - [ ] `ConfigureTimeSystem()` - 配置时间系统
  - [ ] `ConfigureActiveModule()` - 配置显示控制模块
  - [ ] `ConfigureRotationModule()` - 配置旋转模块
  - [ ] `ConfigureScaleModule()` - 配置缩放模块
  - [ ] `ConfigureMaterialAnimation()` - 配置材质动画

**注意事项**：
- 使用GameObject.AddComponent<>()正确添加组件
- 确保组件属性设置的正确性
- 处理组件创建失败的情况

#### 任务3.4：创建Prefab生成器
- [ ] 创建 `PrefabGenerator.cs`
- [ ] 实现 `GeneratePrefab()` 方法 - 主生成方法
- [ ] 实现 `GenerateNewPrefabPath()` 方法 - 生成新Prefab路径
- [ ] 实现 `DisableOriginalParticleSystems()` 方法 - 禁用原始粒子系统
- [ ] 实现 `CreateBackup()` 方法 - 创建备份（如果需要）

**注意事项**：
- 使用PrefabUtility.SaveAsPrefabAsset()保存Prefab
- 正确处理文件路径和命名
- 确保原始Prefab不被修改

### 阶段4：转换控制器 (P0)

#### 任务4.1：创建转换控制器
- [ ] 创建 `ConversionController.cs`
- [ ] 实现 `ConvertParticleSystemToGfx()` 方法 - 主转换方法
- [ ] 实现 `ValidateConversionRequirements()` 方法 - 验证转换前提条件
- [ ] 实现错误处理和异常捕获逻辑
- [ ] 实现转换统计信息收集

**注意事项**：
- 协调各个组件的调用顺序
- 确保数据单向流动
- 提供完整的错误处理和回滚机制

### 阶段5：用户界面 (P1)

#### 任务5.1：创建右键菜单
- [ ] 创建 `ParticleToGfxContextMenu.cs`
- [ ] 实现 `[MenuItem]` 特性的菜单项
- [ ] 实现菜单项验证逻辑
- [ ] 实现转换操作调用
- [ ] 实现用户反馈显示

**注意事项**：
- 使用Unity编辑器扩展API
- 正确验证用户选择的对象
- 提供清晰的操作反馈

### 阶段6：测试和验证 (P2)

#### 任务6.1：创建单元测试
- [ ] 创建 `ParticleSystemAnalyzerTests.cs`
- [ ] 创建 `PropertyMapperTests.cs`
- [ ] 创建 `GfxComponentFactoryTests.cs`
- [ ] 创建 `ParticleSystemValidatorTests.cs`
- [ ] 创建测试数据和Mock对象

**注意事项**：
- 使用Unity Test Framework
- 覆盖正常流程和异常情况
- 确保测试的独立性和可重复性

#### 任务6.2：集成测试
- [ ] 创建端到端转换测试
- [ ] 创建性能测试
- [ ] 创建兼容性测试

### 阶段7：文档和优化 (P2)

#### 任务7.1：完善文档
- [ ] 更新README文件
- [ ] 添加代码注释
- [ ] 创建使用说明

#### 任务7.2：性能优化
- [ ] 分析转换性能
- [ ] 优化内存使用
- [ ] 优化转换速度

---

## 🎯 开发里程碑

### 里程碑1：基础架构完成 (阶段1-2)
- 所有数据结构定义完成
- 基础工具类实现完成
- 编译无错误，基础架构可用

### 里程碑2：核心功能完成 (阶段3-4)
- 转换逻辑实现完成
- 可以成功转换简单的粒子系统
- 基本的错误处理机制工作正常

### 里程碑3：完整功能交付 (阶段5)
- 用户界面完成
- 完整的转换流程可用
- 满足PRD中的所有功能需求

### 里程碑4：质量保证 (阶段6-7)
- 测试覆盖率达标
- 性能满足要求
- 文档完善

## 📋 注意事项

1. **编译验证**：每完成一个任务都要触发Unity编译，确保无错误
2. **依赖管理**：严格按照依赖关系顺序开发，避免循环依赖
3. **代码规范**：遵循项目代码规范和命名约定
4. **测试优先**：核心逻辑要先写测试再写实现
5. **文档同步**：代码变更时同步更新相关文档

## 🚀 开始开发

建议从**任务1.1**开始，按照任务清单的顺序逐步实现。每个任务完成后在清单中标记为 `[x]`，并进行编译验证。
