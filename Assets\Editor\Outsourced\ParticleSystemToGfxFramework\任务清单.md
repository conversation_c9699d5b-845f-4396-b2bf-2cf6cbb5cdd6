# ParticleSystemToGfxFramework 开发任务清单

## 📋 项目概述

基于用户指定的开发流程，将开发任务按照实际转换步骤进行分解，确保每个步骤完成后都可以独立测试，便于尽早发现问题。

## 🎯 开发策略

- **流程驱动**：按照实际转换流程顺序开发
- **步骤可测**：每完成一个步骤都能独立测试验证
- **增量实现**：从最基础的功能开始，逐步增加复杂度
- **快速反馈**：每个步骤都有明确的测试标准

## 🔄 用户指定的开发流程

1. **检测该prefab是否为特效** → 验证GfxRoot_Unity组件
2. **遍历ParticleSystem，筛选可转换的组件** → 找到符合条件的粒子系统
3. **转换基础属性** → mesh filter + mesh renderer + GfxAnimation_Unity时间配置
4. **转换Color over Lifetime** → GfxMaterialAnimation_Unity组件
5. **转换Rotation over Lifetime** → GfxAnimation_Unity旋转模块
6. **转换Size over Lifetime** → GfxAnimation_Unity缩放模块
7. **删除ParticleSystem并保存prefab** → 完成转换

## 📊 任务优先级

### 🟢 P0 - 核心流程 (按顺序完成)
- 每个转换步骤的核心实现
- 基础数据结构支持

### 🟡 P1 - 支撑功能 (配合核心)
- 错误处理和日志
- 用户界面

### 🔵 P2 - 增强功能 (最后完成)
- 单元测试
- 性能优化

---

## 📝 按流程排序的任务清单

### 步骤1：检测Prefab是否为特效 (P0)

#### 任务1.1：创建基础数据结构
- [ ] 创建 `ConversionLogger.cs` - 简单的日志记录器
- [ ] 创建 `GfxFrameworkHelper.cs` - GfxFramework辅助类
- [ ] 实现 `HasGfxRoot(GameObject prefab)` 方法 - 检查是否有GfxRoot_Unity组件

**测试标准**：
- 能够正确检测包含GfxRoot_Unity组件的Prefab
- 能够正确拒绝不包含GfxRoot_Unity组件的Prefab
- 在Console中显示检测结果

**注意事项**：
- 使用 `GetComponent<GfxRoot_Unity>()` 检测组件
- 提供清晰的日志输出
- 处理null值情况

---

### 步骤2：遍历ParticleSystem，筛选可转换的组件 (P0)

#### 任务2.1：创建粒子系统验证和分析
- [ ] 创建 `ParticleSystemValidator.cs` - 粒子系统验证器
- [ ] 实现 `IsValidForConversion(ParticleSystem ps)` 方法
  - [ ] 检查 `MaxParticles == 1`
  - [ ] 检查 `RenderMode == Mesh` 且 `Mesh != null`
  - [ ] 检查禁用模块未启用
- [ ] 创建 `ParticleSystemAnalyzer.cs` - 粒子系统分析器
- [ ] 实现 `FindValidParticleSystems(GameObject prefab)` 方法

**测试标准**：
- 能够找到Prefab中所有的ParticleSystem组件
- 能够正确筛选出符合条件的ParticleSystem
- 在Console中显示找到的粒子系统数量和筛选结果

**注意事项**：
- 使用 `GetComponentsInChildren<ParticleSystem>()` 遍历
- 严格按照PRD筛选条件验证
- 记录每个粒子系统的验证结果

---

### 步骤3：转换基础属性到Mesh组件和GfxAnimation时间 (P0)

#### 任务3.1：创建基础转换功能
- [ ] 创建 `MeshComponentCreator.cs` - Mesh组件创建器
- [ ] 实现 `CreateMeshFilter(GameObject target, Mesh mesh)` 方法
- [ ] 实现 `CreateMeshRenderer(GameObject target, Material material)` 方法
- [ ] 创建 `GfxAnimationCreator.cs` - GfxAnimation创建器
- [ ] 实现 `CreateGfxAnimationWithTime(GameObject target, float startTime, float endTime)` 方法

**测试标准**：
- 能够为GameObject添加MeshFilter和MeshRenderer组件
- 能够正确设置Mesh和Material
- 能够添加GfxAnimation_Unity组件并设置开始/结束时间
- 在Inspector中可以看到新添加的组件

**注意事项**：
- 使用 `AddComponent<>()` 添加组件
- 从ParticleSystemRenderer获取Mesh和Material
- 从ParticleSystem.main获取StartDelay和StartLifetime
- 设置GfxAnimation的m_timeSystem属性

---

### 步骤4：转换Color over Lifetime到GfxMaterialAnimation (P0)

#### 任务4.1：实现颜色动画转换
- [ ] 创建 `ColorAnimationConverter.cs` - 颜色动画转换器
- [ ] 实现 `HasColorOverLifetime(ParticleSystem ps)` 检查方法
- [ ] 实现 `CreateGfxMaterialAnimation(GameObject target, Gradient colorGradient)` 方法
- [ ] 配置GfxMaterialColorCurve的属性名为"_Color"

**测试标准**：
- 能够检测ParticleSystem是否启用了Color over Lifetime
- 能够添加GfxMaterialAnimation_Unity组件
- 能够正确设置颜色渐变曲线
- 在Inspector中可以看到颜色动画配置

**注意事项**：
- 检查 `colorOverLifetime.enabled` 状态
- 获取 `colorOverLifetime.color.gradient`
- 创建GfxMaterialColorCurve并添加到组件
- 设置正确的材质属性名

---

### 步骤5：转换Rotation over Lifetime到GfxAnimation旋转模块 (P0)

#### 任务5.1：实现旋转动画转换
- [ ] 创建 `RotationAnimationConverter.cs` - 旋转动画转换器
- [ ] 实现 `HasRotationOverLifetime(ParticleSystem ps)` 检查方法
- [ ] 实现 `ConfigureRotationModule(GfxAnimation_Unity gfxAnim, ParticleSystem ps)` 方法
- [ ] 处理ParticleSystem.MinMaxCurve到AnimationCurve的转换

**测试标准**：
- 能够检测ParticleSystem是否启用了Rotation over Lifetime
- 能够配置GfxAnimation的旋转模块
- 能够正确转换旋转曲线数据
- 在Inspector中可以看到旋转动画配置

**注意事项**：
- 检查 `rotationOverLifetime.enabled` 状态
- 处理 `separateAxes` 的情况
- 转换MinMaxCurve到AnimationCurve
- 启用GfxRotModule并设置曲线

---

### 步骤6：转换Size over Lifetime到GfxAnimation缩放模块 (P0)

#### 任务6.1：实现缩放动画转换
- [ ] 创建 `ScaleAnimationConverter.cs` - 缩放动画转换器
- [ ] 实现 `HasSizeOverLifetime(ParticleSystem ps)` 检查方法
- [ ] 实现 `ConfigureScaleModule(GfxAnimation_Unity gfxAnim, ParticleSystem ps)` 方法
- [ ] 处理统一缩放和分轴缩放的情况

**测试标准**：
- 能够检测ParticleSystem是否启用了Size over Lifetime
- 能够配置GfxAnimation的缩放模块
- 能够正确转换缩放曲线数据
- 在Inspector中可以看到缩放动画配置

**注意事项**：
- 检查 `sizeOverLifetime.enabled` 状态
- 处理 `separateAxes` 的情况
- 转换MinMaxCurve到AnimationCurve
- 启用GfxScaleModule并设置曲线

---

### 步骤7：删除ParticleSystem并保存Prefab (P0)

#### 任务7.1：完成转换流程
- [ ] 创建 `ConversionFinalizer.cs` - 转换完成器
- [ ] 实现 `DisableParticleSystem(ParticleSystem ps)` 方法
- [ ] 实现 `SaveConvertedPrefab(GameObject prefab, string originalPath)` 方法
- [ ] 生成带_Gfx后缀的新Prefab文件

**测试标准**：
- 能够禁用或删除原始ParticleSystem组件
- 能够保存新的Prefab文件到正确路径
- 新Prefab包含所有转换后的组件
- 在Project窗口中可以看到新生成的Prefab

**注意事项**：
- 使用 `SetActive(false)` 禁用ParticleSystem
- 使用 `PrefabUtility.SaveAsPrefabAsset()` 保存
- 生成正确的文件路径和命名
- 保持原始Prefab不变

---

### 步骤8：创建统一的转换控制器和用户界面 (P1)

#### 任务8.1：整合所有转换步骤
- [ ] 创建 `ConversionController.cs` - 转换控制器
- [ ] 实现 `ConvertPrefab(GameObject prefab)` 主方法
- [ ] 按顺序调用所有转换步骤
- [ ] 创建 `ParticleToGfxContextMenu.cs` - 右键菜单
- [ ] 实现Unity编辑器右键菜单功能

**测试标准**：
- 能够通过右键菜单启动转换
- 完整的转换流程能够正常工作
- 在Console中显示转换结果和统计信息
- 生成的Prefab功能正常

**注意事项**：
- 使用 `[MenuItem]` 特性创建菜单
- 验证用户选择的对象
- 提供完整的错误处理
- 显示转换进度和结果

---


## 🎯 开发里程碑

### 里程碑1：特效检测完成 (步骤1)
- 能够正确检测Prefab是否为特效
- 基础日志和辅助功能可用
- **测试方法**：选择不同类型的Prefab，验证检测结果

### 里程碑2：粒子系统筛选完成 (步骤2)
- 能够找到并筛选符合条件的ParticleSystem
- 验证逻辑准确无误
- **测试方法**：使用包含多个ParticleSystem的Prefab，验证筛选结果

### 里程碑3：基础转换完成 (步骤3)
- 能够创建Mesh组件和GfxAnimation时间配置
- 基础转换流程可用
- **测试方法**：转换简单的粒子系统，检查生成的组件

### 里程碑4：颜色动画转换完成 (步骤4)
- Color over Lifetime转换功能正常
- GfxMaterialAnimation组件配置正确
- **测试方法**：转换带颜色动画的粒子系统

### 里程碑5：旋转动画转换完成 (步骤5)
- Rotation over Lifetime转换功能正常
- GfxAnimation旋转模块配置正确
- **测试方法**：转换带旋转动画的粒子系统

### 里程碑6：缩放动画转换完成 (步骤6)
- Size over Lifetime转换功能正常
- GfxAnimation缩放模块配置正确
- **测试方法**：转换带缩放动画的粒子系统

### 里程碑7：完整转换流程完成 (步骤7)
- 能够删除原始ParticleSystem并保存新Prefab
- 完整的转换流程可用
- **测试方法**：端到端转换测试，验证最终结果

### 里程碑8：用户界面完成 (步骤8)
- 右键菜单功能正常
- 用户可以方便地使用转换工具
- **测试方法**：通过右键菜单完成完整转换

## 📋 每步骤的测试方法

### 🧪 测试策略
1. **单步测试**：每完成一个步骤，立即测试该步骤的功能
2. **增量测试**：在前一步骤基础上测试新功能
3. **回归测试**：确保新功能不影响已有功能
4. **端到端测试**：完整流程的综合测试

### 🔧 测试工具
- **Unity Console**：查看日志输出和错误信息
- **Inspector窗口**：检查组件配置和属性设置
- **Project窗口**：验证生成的Prefab文件
- **Scene窗口**：观察转换后的效果

### 📝 测试数据准备
建议准备以下测试用的Prefab：
1. **基础测试Prefab**：包含GfxRoot_Unity和简单ParticleSystem
2. **颜色动画Prefab**：启用Color over Lifetime的粒子系统
3. **旋转动画Prefab**：启用Rotation over Lifetime的粒子系统
4. **缩放动画Prefab**：启用Size over Lifetime的粒子系统
5. **复合动画Prefab**：同时启用多种动画的粒子系统
6. **无效Prefab**：不符合转换条件的测试用例

## 📋 注意事项

1. **编译验证**：每完成一个任务都要触发Unity编译，确保无错误
2. **步骤测试**：每完成一个步骤都要进行功能测试，确保该步骤正常工作
3. **日志记录**：在每个关键步骤添加日志输出，便于调试和验证
4. **错误处理**：从第一个步骤开始就要考虑错误处理
5. **代码规范**：遵循项目代码规范和命名约定

## 🚀 开始开发

建议从**步骤1：任务1.1**开始，按照步骤顺序逐步实现。每个任务完成后：
1. 在清单中标记为 `[x]`
2. 触发Unity编译验证
3. 进行该步骤的功能测试
4. 记录测试结果和发现的问题
