# ParticleSystemToGfxFramework 数据结构设计文档

## 🎯 设计原则

### 数据单一流向原则
1. **不可变数据对象**：数据在创建后不可修改，通过构造函数或工厂方法创建
2. **单向数据流**：数据只能从上游流向下游，避免循环依赖
3. **明确的数据边界**：每个处理阶段都有明确的输入和输出类型
4. **状态隔离**：避免共享可变状态，每个组件独立处理数据

## 📊 数据流向架构

```mermaid
graph TD
    A[ConversionRequest] --> B[ParticleSystemAnalyzer]
    B --> C[ParticleSystemInfo[]]
    C --> D[PropertyMapper]
    D --> E[GfxComponentData[]]
    E --> F[GfxComponentFactory]
    F --> G[ConversionResult]
    G --> H[PrefabGenerator]
    H --> I[GeneratedPrefabInfo]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style E fill:#f3e5f5
    style G fill:#e8f5e8
    style I fill:#e8f5e8
```

## 🏗️ 核心数据结构

### 1. ConversionRequest (转换请求)
```csharp
/// <summary>
/// 转换请求数据，包含用户输入和转换配置
/// </summary>
public readonly struct ConversionRequest
{
    public readonly GameObject SourcePrefab;
    public readonly ConversionSettings Settings;
    public readonly string OutputPath;
    
    public ConversionRequest(GameObject sourcePrefab, ConversionSettings settings, string outputPath)
    {
        SourcePrefab = sourcePrefab ?? throw new ArgumentNullException(nameof(sourcePrefab));
        Settings = settings ?? ConversionSettings.Default;
        OutputPath = outputPath ?? throw new ArgumentNullException(nameof(outputPath));
    }
}
```

### 2. ParticleSystemInfo (粒子系统信息)
```csharp
/// <summary>
/// 粒子系统的完整信息快照，不可变数据对象
/// </summary>
public readonly struct ParticleSystemInfo
{
    // 基础属性
    public readonly GameObject GameObject;
    public readonly string Name;
    public readonly int MaxParticles;
    public readonly float StartDelay;
    public readonly float StartLifetime;
    
    // 渲染属性
    public readonly RenderingInfo Rendering;
    
    // 动画属性
    public readonly AnimationInfo Animation;
    
    // 模块状态
    public readonly ModuleStates Modules;
    
    public ParticleSystemInfo(GameObject gameObject, ParticleSystem particleSystem)
    {
        GameObject = gameObject;
        Name = gameObject.name;
        
        var main = particleSystem.main;
        MaxParticles = main.maxParticles;
        StartDelay = main.startDelay.constant;
        StartLifetime = main.startLifetime.constant;
        
        Rendering = new RenderingInfo(particleSystem.GetComponent<ParticleSystemRenderer>());
        Animation = new AnimationInfo(particleSystem);
        Modules = new ModuleStates(particleSystem);
    }
}
```

### 3. RenderingInfo (渲染信息)
```csharp
/// <summary>
/// 粒子系统渲染相关信息
/// </summary>
public readonly struct RenderingInfo
{
    public readonly ParticleSystemRenderMode RenderMode;
    public readonly Mesh Mesh;
    public readonly Material Material;
    public readonly bool IsValidForConversion;
    
    public RenderingInfo(ParticleSystemRenderer renderer)
    {
        RenderMode = renderer.renderMode;
        Mesh = renderer.mesh;
        Material = renderer.material;
        IsValidForConversion = RenderMode == ParticleSystemRenderMode.Mesh && Mesh != null;
    }
}
```

### 4. AnimationInfo (动画信息)
```csharp
/// <summary>
/// 粒子系统动画相关信息
/// </summary>
public readonly struct AnimationInfo
{
    public readonly ColorOverLifetimeInfo ColorOverLifetime;
    public readonly RotationOverLifetimeInfo RotationOverLifetime;
    public readonly SizeOverLifetimeInfo SizeOverLifetime;
    
    public AnimationInfo(ParticleSystem particleSystem)
    {
        ColorOverLifetime = new ColorOverLifetimeInfo(particleSystem.colorOverLifetime);
        RotationOverLifetime = new RotationOverLifetimeInfo(particleSystem.rotationOverLifetime);
        SizeOverLifetime = new SizeOverLifetimeInfo(particleSystem.sizeOverLifetime);
    }
}
```

### 5. GfxComponentData (Gfx组件数据)
```csharp
/// <summary>
/// 映射后的Gfx组件配置数据
/// </summary>
public abstract class GfxComponentData
{
    public readonly string ComponentType;
    public readonly GameObject TargetGameObject;
    
    protected GfxComponentData(string componentType, GameObject targetGameObject)
    {
        ComponentType = componentType ?? throw new ArgumentNullException(nameof(componentType));
        TargetGameObject = targetGameObject ?? throw new ArgumentNullException(nameof(targetGameObject));
    }
}

/// <summary>
/// GfxAnimation组件配置数据
/// </summary>
public sealed class GfxAnimationData : GfxComponentData
{
    public readonly GfxTimeData TimeData;
    public readonly GfxActiveModuleData ActiveModule;
    public readonly GfxRotModuleData RotationModule;
    public readonly GfxScaleModuleData ScaleModule;
    
    public GfxAnimationData(GameObject target, GfxTimeData timeData, 
        GfxActiveModuleData activeModule, GfxRotModuleData rotationModule, 
        GfxScaleModuleData scaleModule) 
        : base("GfxAnimation_Unity", target)
    {
        TimeData = timeData;
        ActiveModule = activeModule;
        RotationModule = rotationModule;
        ScaleModule = scaleModule;
    }
}

/// <summary>
/// GfxMaterialAnimation组件配置数据
/// </summary>
public sealed class GfxMaterialAnimationData : GfxComponentData
{
    public readonly GfxTimeData TimeData;
    public readonly GfxMaterialColorCurveData[] ColorCurves;
    
    public GfxMaterialAnimationData(GameObject target, GfxTimeData timeData, 
        GfxMaterialColorCurveData[] colorCurves) 
        : base("GfxMaterialAnimation_Unity", target)
    {
        TimeData = timeData;
        ColorCurves = colorCurves ?? new GfxMaterialColorCurveData[0];
    }
}
```

### 6. 时间和曲线数据结构
```csharp
/// <summary>
/// Gfx时间系统配置数据
/// </summary>
public readonly struct GfxTimeData
{
    public readonly float StartTime;
    public readonly float EndTime;
    public readonly bool Loop;
    public readonly float TimeScale;
    
    public GfxTimeData(float startTime, float endTime, bool loop = false, float timeScale = 1.0f)
    {
        StartTime = startTime;
        EndTime = endTime;
        Loop = loop;
        TimeScale = timeScale;
    }
}

/// <summary>
/// Gfx曲线配置数据
/// </summary>
public readonly struct GfxCurveData
{
    public readonly bool Enable;
    public readonly GfxCurveType CurveType;
    public readonly AnimationCurve Curve;
    public readonly float Speed;
    
    public GfxCurveData(bool enable, AnimationCurve curve)
    {
        Enable = enable;
        CurveType = GfxCurveType.Curve;
        Curve = curve ?? new AnimationCurve();
        Speed = 0f;
    }
    
    public GfxCurveData(bool enable, float speed)
    {
        Enable = enable;
        CurveType = GfxCurveType.Speed;
        Curve = new AnimationCurve();
        Speed = speed;
    }
}

/// <summary>
/// Gfx材质颜色曲线数据
/// </summary>
public readonly struct GfxMaterialColorCurveData
{
    public readonly string PropertyName;
    public readonly Gradient ColorGradient;
    
    public GfxMaterialColorCurveData(string propertyName, Gradient colorGradient)
    {
        PropertyName = propertyName ?? throw new ArgumentNullException(nameof(propertyName));
        ColorGradient = colorGradient ?? throw new ArgumentNullException(nameof(colorGradient));
    }
}
```

### 7. ConversionResult (转换结果)
```csharp
/// <summary>
/// 转换操作的结果信息
/// </summary>
public readonly struct ConversionResult
{
    public readonly bool Success;
    public readonly string Message;
    public readonly ConversionStatistics Statistics;
    public readonly ConversionError[] Errors;
    public readonly ConversionWarning[] Warnings;
    
    public ConversionResult(bool success, string message, ConversionStatistics statistics,
        ConversionError[] errors = null, ConversionWarning[] warnings = null)
    {
        Success = success;
        Message = message ?? string.Empty;
        Statistics = statistics;
        Errors = errors ?? new ConversionError[0];
        Warnings = warnings ?? new ConversionWarning[0];
    }
    
    public static ConversionResult CreateSuccess(ConversionStatistics statistics, string message = "转换成功")
    {
        return new ConversionResult(true, message, statistics);
    }
    
    public static ConversionResult CreateFailure(string message, ConversionError[] errors = null)
    {
        return new ConversionResult(false, message, ConversionStatistics.Empty, errors);
    }
}

/// <summary>
/// 转换统计信息
/// </summary>
public readonly struct ConversionStatistics
{
    public readonly int TotalParticleSystems;
    public readonly int ConvertedParticleSystems;
    public readonly int SkippedParticleSystems;
    public readonly string GeneratedPrefabPath;
    public readonly TimeSpan ConversionTime;
    
    public static readonly ConversionStatistics Empty = new ConversionStatistics(0, 0, 0, string.Empty, TimeSpan.Zero);
    
    public ConversionStatistics(int total, int converted, int skipped, string prefabPath, TimeSpan time)
    {
        TotalParticleSystems = total;
        ConvertedParticleSystems = converted;
        SkippedParticleSystems = skipped;
        GeneratedPrefabPath = prefabPath ?? string.Empty;
        ConversionTime = time;
    }
}
```

## 🔄 数据流转过程

### 阶段1：请求创建
```
用户输入 → ConversionRequest
```

### 阶段2：分析阶段
```
ConversionRequest → ParticleSystemAnalyzer → ParticleSystemInfo[]
```

### 阶段3：映射阶段
```
ParticleSystemInfo[] → PropertyMapper → GfxComponentData[]
```

### 阶段4：创建阶段
```
GfxComponentData[] → GfxComponentFactory → ConversionResult
```

### 阶段5：生成阶段
```
ConversionResult → PrefabGenerator → GeneratedPrefabInfo
```

## 📝 数据验证策略

### 输入验证
- ConversionRequest创建时验证必要参数
- ParticleSystemInfo提取时验证组件有效性
- GfxComponentData创建时验证配置完整性

### 类型安全
- 使用readonly struct确保数据不可变
- 使用强类型避免object类型
- 通过构造函数确保数据完整性

### 错误处理
- 使用Result模式封装成功/失败状态
- 详细的错误信息和警告信息
- 异常安全的数据创建过程

## 🔧 辅助数据结构

### 8. 模块状态信息
```csharp
/// <summary>
/// 粒子系统模块启用状态
/// </summary>
public readonly struct ModuleStates
{
    public readonly bool EmissionEnabled;
    public readonly bool ShapeEnabled;
    public readonly bool VelocityOverLifetimeEnabled;
    public readonly bool LimitVelocityOverLifetimeEnabled;
    public readonly bool InheritVelocityEnabled;
    public readonly bool ForceOverLifetimeEnabled;
    public readonly bool ColorOverLifetimeEnabled;
    public readonly bool ColorBySpeedEnabled;
    public readonly bool SizeOverLifetimeEnabled;
    public readonly bool SizeBySpeedEnabled;
    public readonly bool RotationOverLifetimeEnabled;
    public readonly bool RotationBySpeedEnabled;
    public readonly bool ExternalForcesEnabled;
    public readonly bool NoiseEnabled;
    public readonly bool CollisionEnabled;
    public readonly bool TriggersEnabled;
    public readonly bool SubEmittersEnabled;
    public readonly bool TextureSheetAnimationEnabled;
    public readonly bool LightsEnabled;
    public readonly bool TrailsEnabled;

    public ModuleStates(ParticleSystem particleSystem)
    {
        EmissionEnabled = particleSystem.emission.enabled;
        ShapeEnabled = particleSystem.shape.enabled;
        VelocityOverLifetimeEnabled = particleSystem.velocityOverLifetime.enabled;
        LimitVelocityOverLifetimeEnabled = particleSystem.limitVelocityOverLifetime.enabled;
        InheritVelocityEnabled = particleSystem.inheritVelocity.enabled;
        ForceOverLifetimeEnabled = particleSystem.forceOverLifetime.enabled;
        ColorOverLifetimeEnabled = particleSystem.colorOverLifetime.enabled;
        ColorBySpeedEnabled = particleSystem.colorBySpeed.enabled;
        SizeOverLifetimeEnabled = particleSystem.sizeOverLifetime.enabled;
        SizeBySpeedEnabled = particleSystem.sizeBySpeed.enabled;
        RotationOverLifetimeEnabled = particleSystem.rotationOverLifetime.enabled;
        RotationBySpeedEnabled = particleSystem.rotationBySpeed.enabled;
        ExternalForcesEnabled = particleSystem.externalForces.enabled;
        NoiseEnabled = particleSystem.noise.enabled;
        CollisionEnabled = particleSystem.collision.enabled;
        TriggersEnabled = particleSystem.trigger.enabled;
        SubEmittersEnabled = particleSystem.subEmitters.enabled;
        TextureSheetAnimationEnabled = particleSystem.textureSheetAnimation.enabled;
        LightsEnabled = particleSystem.lights.enabled;
        TrailsEnabled = particleSystem.trails.enabled;
    }

    /// <summary>
    /// 检查是否有禁用的模块被启用
    /// </summary>
    public bool HasForbiddenModulesEnabled()
    {
        return ShapeEnabled || VelocityOverLifetimeEnabled || LimitVelocityOverLifetimeEnabled ||
               InheritVelocityEnabled || ForceOverLifetimeEnabled || ColorBySpeedEnabled ||
               SizeBySpeedEnabled || RotationBySpeedEnabled || ExternalForcesEnabled ||
               NoiseEnabled || CollisionEnabled || TriggersEnabled || SubEmittersEnabled ||
               TextureSheetAnimationEnabled || LightsEnabled || TrailsEnabled;
    }
}
```

### 9. 动画模块数据结构
```csharp
/// <summary>
/// Color over Lifetime 信息
/// </summary>
public readonly struct ColorOverLifetimeInfo
{
    public readonly bool Enabled;
    public readonly Gradient ColorGradient;

    public ColorOverLifetimeInfo(ParticleSystem.ColorOverLifetimeModule module)
    {
        Enabled = module.enabled;
        ColorGradient = Enabled ? module.color.gradient : new Gradient();
    }
}

/// <summary>
/// Rotation over Lifetime 信息
/// </summary>
public readonly struct RotationOverLifetimeInfo
{
    public readonly bool Enabled;
    public readonly ParticleSystem.MinMaxCurve AngularVelocity;
    public readonly bool SeparateAxes;
    public readonly ParticleSystem.MinMaxCurve X;
    public readonly ParticleSystem.MinMaxCurve Y;
    public readonly ParticleSystem.MinMaxCurve Z;

    public RotationOverLifetimeInfo(ParticleSystem.RotationOverLifetimeModule module)
    {
        Enabled = module.enabled;
        AngularVelocity = module.angularVelocity;
        SeparateAxes = module.separateAxes;
        X = module.x;
        Y = module.y;
        Z = module.z;
    }
}

/// <summary>
/// Size over Lifetime 信息
/// </summary>
public readonly struct SizeOverLifetimeInfo
{
    public readonly bool Enabled;
    public readonly ParticleSystem.MinMaxCurve Size;
    public readonly bool SeparateAxes;
    public readonly ParticleSystem.MinMaxCurve X;
    public readonly ParticleSystem.MinMaxCurve Y;
    public readonly ParticleSystem.MinMaxCurve Z;

    public SizeOverLifetimeInfo(ParticleSystem.SizeOverLifetimeModule module)
    {
        Enabled = module.enabled;
        Size = module.size;
        SeparateAxes = module.separateAxes;
        X = module.x;
        Y = module.y;
        Z = module.z;
    }
}
```

### 10. Gfx模块配置数据
```csharp
/// <summary>
/// GfxActiveModule配置数据
/// </summary>
public readonly struct GfxActiveModuleData
{
    public readonly bool Enable;
    public readonly float ActivePercent;
    public readonly float DisactivePercent;

    public GfxActiveModuleData(bool enable, float activePercent = 0.0f, float disactivePercent = 1.0f)
    {
        Enable = enable;
        ActivePercent = activePercent;
        DisactivePercent = disactivePercent;
    }
}

/// <summary>
/// GfxRotModule配置数据
/// </summary>
public readonly struct GfxRotModuleData
{
    public readonly bool Enable;
    public readonly bool AlwaysFaceCamera;
    public readonly bool WorldSpace;
    public readonly GfxAxis FaceCameraAxis;
    public readonly GfxCurveData XCurve;
    public readonly GfxCurveData YCurve;
    public readonly GfxCurveData ZCurve;

    public GfxRotModuleData(bool enable, GfxCurveData xCurve, GfxCurveData yCurve, GfxCurveData zCurve,
        bool alwaysFaceCamera = false, bool worldSpace = true, GfxAxis faceCameraAxis = GfxAxis.Forward)
    {
        Enable = enable;
        AlwaysFaceCamera = alwaysFaceCamera;
        WorldSpace = worldSpace;
        FaceCameraAxis = faceCameraAxis;
        XCurve = xCurve;
        YCurve = yCurve;
        ZCurve = zCurve;
    }
}

/// <summary>
/// GfxScaleModule配置数据
/// </summary>
public readonly struct GfxScaleModuleData
{
    public readonly bool Enable;
    public readonly bool UniformScale;
    public readonly GfxCurveData XCurve;
    public readonly GfxCurveData YCurve;
    public readonly GfxCurveData ZCurve;

    public GfxScaleModuleData(bool enable, GfxCurveData xCurve, GfxCurveData yCurve, GfxCurveData zCurve,
        bool uniformScale = true)
    {
        Enable = enable;
        UniformScale = uniformScale;
        XCurve = xCurve;
        YCurve = yCurve;
        ZCurve = zCurve;
    }
}
```

### 11. 错误和警告信息
```csharp
/// <summary>
/// 转换错误信息
/// </summary>
public readonly struct ConversionError
{
    public readonly string Code;
    public readonly string Message;
    public readonly string GameObject;
    public readonly Exception Exception;

    public ConversionError(string code, string message, string gameObject = null, Exception exception = null)
    {
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Message = message ?? throw new ArgumentNullException(nameof(message));
        GameObject = gameObject ?? string.Empty;
        Exception = exception;
    }
}

/// <summary>
/// 转换警告信息
/// </summary>
public readonly struct ConversionWarning
{
    public readonly string Code;
    public readonly string Message;
    public readonly string GameObject;

    public ConversionWarning(string code, string message, string gameObject = null)
    {
        Code = code ?? throw new ArgumentNullException(nameof(code));
        Message = message ?? throw new ArgumentNullException(nameof(message));
        GameObject = gameObject ?? string.Empty;
    }
}
```

### 12. 转换设置
```csharp
/// <summary>
/// 转换设置配置
/// </summary>
public readonly struct ConversionSettings
{
    public readonly bool GenerateBackup;
    public readonly bool OverwriteExisting;
    public readonly string OutputSuffix;
    public readonly bool ValidateResult;
    public readonly LogLevel LogLevel;

    public static readonly ConversionSettings Default = new ConversionSettings(
        generateBackup: true,
        overwriteExisting: false,
        outputSuffix: "_Gfx",
        validateResult: true,
        logLevel: LogLevel.Info
    );

    public ConversionSettings(bool generateBackup, bool overwriteExisting, string outputSuffix,
        bool validateResult, LogLevel logLevel)
    {
        GenerateBackup = generateBackup;
        OverwriteExisting = overwriteExisting;
        OutputSuffix = outputSuffix ?? "_Gfx";
        ValidateResult = validateResult;
        LogLevel = logLevel;
    }
}

/// <summary>
/// 日志级别
/// </summary>
public enum LogLevel
{
    Error = 0,
    Warning = 1,
    Info = 2,
    Debug = 3
}
```

## 🎯 数据流向保证机制

### 1. 不可变性保证
- 所有数据结构使用`readonly struct`或`readonly`字段
- 通过构造函数一次性初始化所有数据
- 避免setter方法，只提供getter访问

### 2. 类型安全保证
- 使用强类型避免运行时类型错误
- 通过泛型确保类型一致性
- 编译时检查数据结构完整性

### 3. 数据验证保证
- 构造函数中进行参数验证
- 提供IsValid方法检查数据有效性
- 使用Result模式处理可能失败的操作

### 4. 内存安全保证
- 避免循环引用
- 及时释放大对象引用
- 使用值类型减少GC压力
