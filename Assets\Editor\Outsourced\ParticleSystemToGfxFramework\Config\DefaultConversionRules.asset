%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f52912c59943cc84b8929ed65e66cb16, type: 3}
  m_Name: DefaultConversionRules
  m_EditorClassIdentifier: 
  version: 1.0
  description: "\u9ED8\u8BA4\u7C92\u5B50\u7CFB\u7EDF\u8F6C\u6362\u9A8C\u8BC1\u89C4\u5219"
  basicRules:
  - name: maxParticles
    description: 
    propertyPath: main.maxParticles
    comparisonOperator: 0
    expectedValue: 1
    severity: 2
    errorMessage: "Max Particles\u5FC5\u987B\u7B49\u4E8E1"
  moduleRules:
  - name: forbiddenModules
    description: 
    moduleNames:
    - shape
    - velocityOverLifetime
    - limitVelocityOverLifetime
    - inheritVelocity
    - forceOverLifetime
    - colorBySpeed
    - sizeBySpeed
    - rotationBySpeed
    - externalForces
    - noise
    - collision
    - trigger
    - subEmitters
    - textureSheetAnimation
    - lights
    - trails
    allowEnabled: 0
    severity: 2
    errorMessage: "\u7981\u6B62\u542F\u7528\u7684\u6A21\u5757: {moduleName}"
  expressionRules:
  - name: renderModeAndMesh
    description: 
    expression: renderer.renderMode == 4 && renderer.mesh != null
    severity: 2
    errorMessage: "\u5FC5\u987B\u4F7F\u7528Mesh\u6E32\u67D3\u6A21\u5F0F\u4E14Mesh\u4E0D\u4E3A\u7A7A"
