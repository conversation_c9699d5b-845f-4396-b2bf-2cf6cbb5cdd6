# 数据流向实现示例

## 🔄 完整数据流转示例

### 1. 输入层：创建转换请求
```csharp
// 用户在UI层选择Prefab后，创建转换请求
public static ConversionRequest CreateConversionRequest(GameObject selectedPrefab)
{
    var settings = ConversionSettings.Default;
    var outputPath = AssetDatabase.GetAssetPath(selectedPrefab);
    
    return new ConversionRequest(selectedPrefab, settings, outputPath);
}
```

### 2. 分析层：提取粒子系统信息
```csharp
public class ParticleSystemAnalyzer
{
    public ParticleSystemInfo[] AnalyzePrefab(ConversionRequest request)
    {
        var particleSystems = request.SourcePrefab.GetComponentsInChildren<ParticleSystem>();
        var results = new List<ParticleSystemInfo>();
        
        foreach (var ps in particleSystems)
        {
            // 创建不可变的粒子系统信息快照
            var info = new ParticleSystemInfo(ps.gameObject, ps);
            
            // 只添加符合条件的粒子系统
            if (IsValidForConversion(info))
            {
                results.Add(info);
            }
        }
        
        return results.ToArray();
    }
    
    private bool IsValidForConversion(ParticleSystemInfo info)
    {
        return info.MaxParticles == 1 && 
               info.Rendering.IsValidForConversion && 
               !info.Modules.HasForbiddenModulesEnabled();
    }
}
```

### 3. 映射层：转换为Gfx组件数据
```csharp
public class PropertyMapper
{
    public GfxComponentData[] MapToGfxComponents(ParticleSystemInfo[] particleInfos)
    {
        var results = new List<GfxComponentData>();
        
        foreach (var info in particleInfos)
        {
            // 映射时间数据
            var timeData = MapTimeData(info);
            
            // 映射GfxAnimation组件
            if (NeedsGfxAnimation(info))
            {
                var animationData = MapToGfxAnimation(info, timeData);
                results.Add(animationData);
            }
            
            // 映射GfxMaterialAnimation组件
            if (NeedsGfxMaterialAnimation(info))
            {
                var materialAnimationData = MapToGfxMaterialAnimation(info, timeData);
                results.Add(materialAnimationData);
            }
            
            // 映射Mesh组件
            var meshData = MapToMeshComponents(info);
            results.Add(meshData);
        }
        
        return results.ToArray();
    }
    
    private GfxTimeData MapTimeData(ParticleSystemInfo info)
    {
        return new GfxTimeData(
            startTime: info.StartDelay,
            endTime: info.StartDelay + info.StartLifetime,
            loop: false,
            timeScale: 1.0f
        );
    }
    
    private GfxAnimationData MapToGfxAnimation(ParticleSystemInfo info, GfxTimeData timeData)
    {
        // 映射显示控制模块
        var activeModule = new GfxActiveModuleData(
            enable: true,
            activePercent: 0.0f,
            disactivePercent: 1.0f
        );
        
        // 映射旋转模块
        var rotationModule = MapRotationModule(info.Animation.RotationOverLifetime);
        
        // 映射缩放模块
        var scaleModule = MapScaleModule(info.Animation.SizeOverLifetime);
        
        return new GfxAnimationData(
            target: info.GameObject,
            timeData: timeData,
            activeModule: activeModule,
            rotationModule: rotationModule,
            scaleModule: scaleModule
        );
    }
    
    private GfxMaterialAnimationData MapToGfxMaterialAnimation(ParticleSystemInfo info, GfxTimeData timeData)
    {
        var colorCurves = new List<GfxMaterialColorCurveData>();
        
        if (info.Animation.ColorOverLifetime.Enabled)
        {
            var colorCurve = new GfxMaterialColorCurveData(
                propertyName: "_Color",
                colorGradient: info.Animation.ColorOverLifetime.ColorGradient
            );
            colorCurves.Add(colorCurve);
        }
        
        return new GfxMaterialAnimationData(
            target: info.GameObject,
            timeData: timeData,
            colorCurves: colorCurves.ToArray()
        );
    }
}
```

### 4. 创建层：生成Unity组件
```csharp
public class GfxComponentFactory
{
    public ConversionResult CreateComponents(GfxComponentData[] componentDataArray)
    {
        var errors = new List<ConversionError>();
        var warnings = new List<ConversionWarning>();
        var statistics = new ConversionStatistics();
        
        try
        {
            foreach (var componentData in componentDataArray)
            {
                switch (componentData)
                {
                    case GfxAnimationData animData:
                        CreateGfxAnimation(animData);
                        break;
                        
                    case GfxMaterialAnimationData matAnimData:
                        CreateGfxMaterialAnimation(matAnimData);
                        break;
                        
                    case MeshComponentData meshData:
                        CreateMeshComponents(meshData);
                        break;
                        
                    default:
                        warnings.Add(new ConversionWarning(
                            "UNKNOWN_COMPONENT", 
                            $"未知的组件类型: {componentData.ComponentType}"
                        ));
                        break;
                }
            }
            
            return ConversionResult.CreateSuccess(statistics, "组件创建成功");
        }
        catch (Exception ex)
        {
            errors.Add(new ConversionError("CREATION_FAILED", "组件创建失败", exception: ex));
            return ConversionResult.CreateFailure("组件创建过程中发生错误", errors.ToArray());
        }
    }
    
    private void CreateGfxAnimation(GfxAnimationData data)
    {
        var gfxAnim = data.TargetGameObject.AddComponent<GfxAnimation_Unity>();
        
        // 配置时间系统
        gfxAnim.m_timeSystem.m_startTime = data.TimeData.StartTime;
        gfxAnim.m_timeSystem.m_endTime = data.TimeData.EndTime;
        gfxAnim.m_timeSystem.m_loop = data.TimeData.Loop;
        gfxAnim.m_timeSystem.m_timeScale = data.TimeData.TimeScale;
        
        // 配置显示控制模块
        gfxAnim.m_activeModule.m_enable = data.ActiveModule.Enable;
        gfxAnim.m_activeModule.m_activePercent = data.ActiveModule.ActivePercent;
        gfxAnim.m_activeModule.m_disactivePercent = data.ActiveModule.DisactivePercent;
        
        // 配置旋转模块
        ConfigureRotationModule(gfxAnim.m_rotModule, data.RotationModule);
        
        // 配置缩放模块
        ConfigureScaleModule(gfxAnim.m_scaleModule, data.ScaleModule);
    }
    
    private void CreateGfxMaterialAnimation(GfxMaterialAnimationData data)
    {
        var gfxMatAnim = data.TargetGameObject.AddComponent<GfxMaterialAnimation_Unity>();
        
        // 配置时间系统
        gfxMatAnim.m_timeSystem.m_startTime = data.TimeData.StartTime;
        gfxMatAnim.m_timeSystem.m_endTime = data.TimeData.EndTime;
        
        // 配置颜色曲线
        foreach (var colorCurveData in data.ColorCurves)
        {
            var colorCurve = new GfxMaterialColorCurve();
            colorCurve.m_propertyName = colorCurveData.PropertyName;
            colorCurve.m_colorCurve = colorCurveData.ColorGradient;
            
            gfxMatAnim.m_materialColorCurves.Add(colorCurve);
        }
    }
}
```

### 5. 生成层：创建新Prefab
```csharp
public class PrefabGenerator
{
    public GeneratedPrefabInfo GeneratePrefab(ConversionResult conversionResult, ConversionRequest request)
    {
        if (!conversionResult.Success)
        {
            throw new InvalidOperationException("无法从失败的转换结果生成Prefab");
        }
        
        try
        {
            // 生成新的Prefab路径
            var originalPath = AssetDatabase.GetAssetPath(request.SourcePrefab);
            var newPath = GenerateNewPrefabPath(originalPath, request.Settings.OutputSuffix);
            
            // 创建Prefab副本
            var newPrefab = PrefabUtility.SaveAsPrefabAsset(request.SourcePrefab, newPath);
            
            // 禁用原始ParticleSystem组件
            DisableOriginalParticleSystems(newPrefab);
            
            return new GeneratedPrefabInfo(
                prefabPath: newPath,
                originalPath: originalPath,
                backupCreated: request.Settings.GenerateBackup
            );
        }
        catch (Exception ex)
        {
            throw new PrefabGenerationException("Prefab生成失败", ex);
        }
    }
    
    private string GenerateNewPrefabPath(string originalPath, string suffix)
    {
        var directory = Path.GetDirectoryName(originalPath);
        var fileName = Path.GetFileNameWithoutExtension(originalPath);
        var extension = Path.GetExtension(originalPath);
        
        return Path.Combine(directory, $"{fileName}{suffix}{extension}");
    }
    
    private void DisableOriginalParticleSystems(GameObject prefab)
    {
        var particleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
        foreach (var ps in particleSystems)
        {
            ps.gameObject.SetActive(false);
        }
    }
}
```

## 🎯 数据流向保证机制实现

### 1. 不可变数据结构示例
```csharp
// 所有数据结构都是readonly，确保数据不可变
public readonly struct ParticleSystemInfo
{
    public readonly GameObject GameObject;
    public readonly string Name;
    public readonly int MaxParticles;
    // ... 其他只读字段
    
    // 只能通过构造函数创建，确保数据完整性
    public ParticleSystemInfo(GameObject gameObject, ParticleSystem particleSystem)
    {
        GameObject = gameObject ?? throw new ArgumentNullException(nameof(gameObject));
        // ... 初始化所有字段
    }
}
```

### 2. 单向数据流控制
```csharp
public class ConversionController
{
    public ConversionResult Convert(ConversionRequest request)
    {
        // 数据只能单向流动：request → info → data → result
        var particleInfos = _analyzer.AnalyzePrefab(request);
        var componentData = _mapper.MapToGfxComponents(particleInfos);
        var result = _factory.CreateComponents(componentData);
        
        return result;
    }
}
```

### 3. 类型安全保证
```csharp
// 使用强类型避免运行时错误
public abstract class GfxComponentData
{
    public readonly string ComponentType;
    protected GfxComponentData(string componentType)
    {
        ComponentType = componentType ?? throw new ArgumentNullException(nameof(componentType));
    }
}

// 具体类型继承，确保类型安全
public sealed class GfxAnimationData : GfxComponentData
{
    public GfxAnimationData(/* 参数 */) : base("GfxAnimation_Unity") { }
}
```

## 📊 数据流向监控

### 性能监控
```csharp
public class ConversionPerformanceMonitor
{
    public ConversionResult MonitorConversion(ConversionRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = _controller.Convert(request);
            stopwatch.Stop();
            
            // 记录性能数据
            LogPerformanceMetrics(result, stopwatch.Elapsed);
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            LogError(ex, stopwatch.Elapsed);
            throw;
        }
    }
}
```
