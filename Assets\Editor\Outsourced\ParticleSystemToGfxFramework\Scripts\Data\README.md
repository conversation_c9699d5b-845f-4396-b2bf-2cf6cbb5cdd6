# Data 数据模型层

## 📋 层级用途

Data层是ParticleSystemToGfxFramework转换工具的数据模型层，定义了转换过程中使用的所有数据结构和模型。

## 🎯 设计职责

- **数据结构定义**：定义清晰的数据模型和结构
- **数据传输对象**：在不同层之间传递数据
- **类型安全**：提供强类型的数据访问
- **数据验证**：包含基本的数据验证逻辑

## 📁 文件说明

### ConversionData.cs
- **职责**：转换过程的配置和数据容器
- **包含内容**：
  - 转换配置参数
  - 源ParticleSystem信息
  - 目标GfxFramework组件配置
  - 转换选项和设置

### ParticleSystemInfo.cs
- **职责**：封装ParticleSystem组件的关键信息
- **包含内容**：
  - 基础属性（Max Particles、Start Delay、Start Lifetime等）
  - 渲染属性（Mesh、Material、Render Mode等）
  - 动画属性（Color/Rotation/Size over Lifetime等）
  - 模块启用状态信息

### ConversionResult.cs
- **职责**：封装转换操作的结果信息
- **包含内容**：
  - 转换是否成功
  - 转换的粒子系统数量
  - 生成的新Prefab路径
  - 错误信息和警告
  - 转换统计数据

## 🏗️ 设计模式

### 数据传输对象（DTO）模式
- 所有Data类都是纯数据容器
- 不包含业务逻辑，只包含数据和基本验证
- 便于序列化和网络传输

### 值对象模式
- 数据模型是不可变的值对象
- 通过构造函数或工厂方法创建
- 提供只读属性访问

## 📝 编码规范

### 属性定义
```csharp
// 使用只读属性
public string PropertyName { get; }

// 或使用私有setter
public string PropertyName { get; private set; }
```

### 构造函数
```csharp
// 通过构造函数初始化所有必要属性
public ConversionData(string name, bool isValid)
{
    Name = name;
    IsValid = isValid;
}
```

### 验证方法
```csharp
// 提供数据验证方法
public bool IsValid()
{
    return !string.IsNullOrEmpty(Name) && /* 其他验证条件 */;
}
```

## 🔗 依赖关系

- **依赖**：Unity引擎类型（ParticleSystem、GameObject等）
- **被依赖**：Core层、Utils层、UI层

## 🧪 测试策略

- 测试数据模型的创建和属性访问
- 测试数据验证逻辑
- 测试序列化和反序列化（如果需要）

## 📋 注意事项

1. **不可变性**：尽量保持数据模型的不可变性
2. **空值处理**：合理处理null值和默认值
3. **类型安全**：使用强类型，避免object类型
4. **文档注释**：为所有公开属性添加XML文档注释
5. **命名规范**：使用清晰、描述性的属性名称
