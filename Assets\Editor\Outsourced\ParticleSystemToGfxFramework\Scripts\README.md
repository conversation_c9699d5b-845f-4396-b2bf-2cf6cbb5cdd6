# Scripts 核心代码目录

## 📋 目录用途

Scripts目录是ParticleSystemToGfxFramework转换工具的核心代码目录，包含所有业务逻辑、数据模型、用户界面和工具类。

## 🏗️ 架构设计

本项目采用**分层架构**和**MVC模式**，确保代码的可维护性、可测试性和可扩展性。

```
Scripts/
├── 📁 Core/        # 核心业务逻辑层
├── 📁 Data/        # 数据模型层  
├── 📁 UI/          # 用户界面层
└── 📁 Utils/       # 工具类层
```

## 🎯 设计原则

### SOLID原则
- **S** - 单一职责原则：每个类只负责一个明确的功能
- **O** - 开闭原则：对扩展开放，对修改关闭
- **L** - 里氏替换原则：子类可以替换父类
- **I** - 接口隔离原则：使用多个专门的接口
- **D** - 依赖倒置原则：依赖抽象而非具体实现

### MVC分离
- **Model（Data层）**：数据模型和业务实体
- **View（UI层）**：用户界面和交互逻辑
- **Controller（Core层）**：业务逻辑控制和协调

### 模块化设计
- **高内聚**：相关功能聚集在同一模块内
- **低耦合**：模块间依赖关系清晰且最小化
- **可测试**：每个模块都可以独立测试

## 🔗 层级依赖关系

```mermaid
graph TD
    A[UI Layer] --> B[Core Layer]
    B --> C[Data Layer]
    B --> D[Utils Layer]
    C --> E[Unity Engine]
    D --> E
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

### 依赖规则
1. **UI层**：只能依赖Core层，不能直接访问Data层和Utils层
2. **Core层**：可以依赖Data层和Utils层，但不能依赖UI层
3. **Data层**：只能依赖Unity引擎，不能依赖其他业务层
4. **Utils层**：只能依赖Unity引擎和.NET基础类库

## 📦 核心组件

### ConversionController（转换控制器）
- **位置**：Core/ConversionController.cs
- **职责**：统一控制转换流程，协调各个组件
- **关键方法**：
  - `ConvertParticleSystemToGfx(GameObject prefab)`
  - `ValidateConversionRequirements(GameObject prefab)`

### 数据流转
```
UI层接收用户输入 
    ↓
Controller协调转换流程
    ↓
Analyzer分析粒子系统 → PropertyMapper映射属性 → Factory创建组件 → Generator生成Prefab
    ↓
返回转换结果给UI层
```

## 🛠️ 开发规范

### 命名约定
- **类名**：PascalCase（如：ParticleSystemAnalyzer）
- **方法名**：PascalCase（如：ConvertParticleSystem）
- **字段名**：camelCase，私有字段加下划线前缀（如：_analyzer）
- **常量**：UPPER_CASE（如：MAX_PARTICLES）

### 代码组织
```csharp
// 文件头部注释
/// <summary>
/// 粒子系统分析器，负责分析和筛选符合条件的ParticleSystem组件
/// </summary>

// 命名空间
namespace ParticleSystemToGfxFramework.Core
{
    // 类定义
    public class ParticleSystemAnalyzer
    {
        // 私有字段
        private readonly ILogger _logger;
        
        // 构造函数
        public ParticleSystemAnalyzer(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        // 公开方法
        public List<ParticleSystemInfo> AnalyzePrefab(GameObject prefab)
        {
            // 实现逻辑
        }
        
        // 私有方法
        private bool IsValidParticleSystem(ParticleSystem ps)
        {
            // 实现逻辑
        }
    }
}
```

### 异常处理
```csharp
public ConversionResult ConvertParticleSystem(GameObject prefab)
{
    try
    {
        // 转换逻辑
        return new ConversionResult(true, "转换成功");
    }
    catch (ArgumentNullException ex)
    {
        _logger.LogError($"参数为空：{ex.ParamName}");
        return new ConversionResult(false, "输入参数无效");
    }
    catch (Exception ex)
    {
        _logger.LogError($"转换失败：{ex.Message}");
        return new ConversionResult(false, "转换过程中发生未知错误");
    }
}
```

## 🧪 测试策略

### 单元测试
- 每个Core类都有对应的测试类
- 测试文件命名：`{ClassName}Tests.cs`
- 测试方法命名：`{MethodName}_{Condition}_{ExpectedResult}`

### 集成测试
- 测试不同层之间的交互
- 测试完整的转换流程
- 使用真实的Unity组件

### Mock使用
```csharp
[Test]
public void ConvertParticleSystem_WithValidInput_CallsAnalyzer()
{
    // Arrange
    var mockAnalyzer = new Mock<IParticleSystemAnalyzer>();
    var controller = new ConversionController(mockAnalyzer.Object);
    
    // Act
    controller.ConvertParticleSystem(testPrefab);
    
    // Assert
    mockAnalyzer.Verify(a => a.AnalyzePrefab(testPrefab), Times.Once);
}
```

## 📝 文档要求

### XML文档注释
```csharp
/// <summary>
/// 分析指定的Prefab，查找并筛选符合转换条件的ParticleSystem组件
/// </summary>
/// <param name="prefab">要分析的Prefab对象</param>
/// <returns>符合条件的ParticleSystem信息列表</returns>
/// <exception cref="ArgumentNullException">当prefab为null时抛出</exception>
public List<ParticleSystemInfo> AnalyzePrefab(GameObject prefab)
```

### README文档
- 每个子目录都有README.md文件
- 说明目录用途、设计思路和使用方法
- 提供代码示例和最佳实践

## 🚀 构建和部署

### 编译检查
```bash
# 触发Unity编译
./WatchCompile.sh

# 检查编译错误和警告
tail -f Editor.log | grep -E "(error|warning)"
```

### 代码质量
- 使用Rider进行代码分析
- 遵循C# 7.3语法规范
- 保持代码覆盖率在80%以上

## 📋 开发检查清单

- [ ] 代码遵循命名约定和组织规范
- [ ] 每个公开方法都有XML文档注释
- [ ] 异常处理完善，记录详细日志
- [ ] 单元测试覆盖核心功能
- [ ] 代码通过静态分析检查
- [ ] 依赖关系清晰，避免循环依赖
- [ ] 性能考虑，避免不必要的计算
- [ ] 线程安全，正确处理并发访问
