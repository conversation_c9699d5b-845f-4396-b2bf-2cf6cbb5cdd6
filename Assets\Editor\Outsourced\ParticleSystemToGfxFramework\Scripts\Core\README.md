# Core 核心转换逻辑层

## 📋 层级用途

Core层是ParticleSystemToGfxFramework转换工具的核心业务逻辑层，负责实现粒子系统到GfxFramework的转换核心功能。

## 🎯 设计职责

- **业务逻辑封装**：将复杂的转换逻辑封装在独立的类中
- **单一职责原则**：每个类只负责一个明确的转换功能
- **可测试性**：核心逻辑与UI和依赖解耦，便于单元测试
- **可复用性**：核心逻辑可在其他项目或工具中复用

## 📁 文件说明

### ParticleSystemAnalyzer.cs
- **职责**：分析和筛选符合条件的ParticleSystem组件
- **功能**：
  - 扫描Prefab中的所有ParticleSystem组件
  - 根据PRD中的筛选条件过滤有效的粒子系统
  - 提取粒子系统的关键属性信息

### GfxComponentFactory.cs
- **职责**：创建和配置GfxFramework相关组件
- **功能**：
  - 创建GfxAnimation_Unity组件并配置属性
  - 创建GfxMaterialAnimation_Unity组件并配置材质动画
  - 创建MeshFilter和MeshRenderer组件

### PropertyMapper.cs
- **职责**：将ParticleSystem属性映射到GfxFramework属性
- **功能**：
  - 时间属性映射（Start Delay → 开始时间，Start Lifetime → 结束时间）
  - 动画属性映射（Color/Rotation/Size over Lifetime → Gfx动画）
  - 渲染属性映射（Mesh → MeshFilter，Material → MeshRenderer）

### PrefabGenerator.cs
- **职责**：生成新的Prefab文件
- **功能**：
  - 复制原始Prefab并应用转换
  - 生成带_Gfx后缀的新Prefab文件
  - 保存到原目录下

## 🔗 依赖关系

- **依赖**：Data层的数据模型、Utils层的工具类
- **被依赖**：Controller层调用Core层的服务

## 🧪 测试策略

- 每个Core类都应有对应的单元测试
- 测试覆盖正常流程和异常情况
- 使用Mock对象隔离外部依赖

## 📝 开发规范

- 遵循单一职责原则，每个类功能明确
- 使用依赖注入，便于测试和扩展
- 添加详细的XML文档注释
- 异常处理要完善，记录详细日志
